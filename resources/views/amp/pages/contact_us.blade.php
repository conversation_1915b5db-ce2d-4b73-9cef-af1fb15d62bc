@extends('amp.layout')

@section('title', $page->seo_title ?? 'Keep in Touch')

@section('description', $page->seo_description ?? 'Contact Me to make interesting work together. Enjoy with our services
    .')

@section('keywords', $page->seo_keywords)
@section('amp_url', $page->ampUrl)


@section('meta')
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@zaherkhirullah">
    <meta name="twitter:creator" content="@zaherkhirullah">
    <meta name="twitter:title" content="{{ str_replace('"', '', $page->title) }}">
    <meta name="twitter:description" content="{{ str_replace('"', '', $page->seo_description) }}">
    <meta name="twitter:image" content="{{ $page->imagePath }}">
    <meta property="og:type" content="article">
    <meta property="og:title" content="{{ $page->title }}" />
    <meta property="og:url" content="{{ $page->url }}">
    <meta property="og:site_name" content="{{ get_setting('site_name') }}">
    <meta property="og:image" content="{{ $page->imagePath }}">
    <meta property="og:image:alt" content="{{ $page->title }}" />
    <meta property="og:description" content="{{ str_replace('"', '', $page->seo_description) }}" />
    <meta property="og:locale" content="en_GB" />
@endsection

@section('schema')
    <?php $replace = ['"', '&nbsp;']; ?>
    <script data-schema="NewsArticle" type="application/ld+json">{
        "@context": "http://schema.org",
        "@type": "NewsArticle",
        "mainEntityOfPage": {"@type": "WebPage", "@id": "{{ $page->url }}" },
        "headline": "{{ $page->title }}",
        "url": "{{ $page->url }}",
        "image": { "@type": "ImageObject", "url":"{{$page->imagePath}}", "height": 360, "width": 640 },
        "dateCreated": "{{$page->created_at }}",
        "datePublished": "{{$page->created_at }}",
        "dateModified": "{{ $page->updated_at!='' ? $page->updated_at : $page->created_at }}",
        "articleBody": "{!! str_replace($replace,"",strip_tags($page->content))  !!}",
        "author": { "@type": "Person", "name": "{{ get_setting('site_name') }}" },
         "publisher": {
          "@type": "Organization",
          "name": "{{ config('app.name') }}",
          "logo": {"@type": "ImageObject", "url": "{{ url('assets/img/logo.png') }}", "width": 60, "height": 60 }
        },
        "description": "{{$page->seo_description }}"
      }
    </script>
    <script type="application/ld+json">
  {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
      {"@type": "ListItem", "position": 1, "name": "{{ get_setting('site_name')   }}", "item": "{{ url('/') }}"},
      {"@type": "ListItem", "position": 2, "name": "{{$page->title}}", "item": "{{ $page->url }}"}]
    }
    </script>
@endsection
@section('content')
    <header class="headerbar text-center">
        <h1> Get In Touch </h1>
    </header>
    <div class="main-text text-center h2"> </div>
    <p class="main-content">
        <amp-img alt="{{ get_setting('site_name') }}"
            src="{{ asset_theme_v('assets/img/svg/undraw_social_dashboard_k3pt.svg') }}" width="500" height="480"
            layout="responsive">
            {{--            <div fallback>Cannot play animated images on this device.</div> --}}
        </amp-img>

    <div class="d-flex flex-wrap my-1">
        <a href="tel:00905511463411" class="btn w-100 button secondary-button text-capitalize m-2" target="_blank"> <i
                class="fas fa-phone-alt"></i> Call me</a>
        <a href="https://api.whatsapp.com/send?phone=905511463411"
            class="btn w-100 button primary-button text-capitalize mt-1" target="_blank"> <i class="fab fa-whatsapp"></i>
            Whatsapp</a>
    </div>
    <div class="below-hero">
        <h2 class="main-heading h4">{{ $page->title }}</h2>
        <p class="main-text">

        </p>
    </div>

@stop
