<section id="about" class="about-area mb-4">
    <div class="row align-items-center">
        <div class="col-12 col-lg-6">
            <div class="image-wrapper about-image mb-2">
                <img data-src="{{ asset_theme_v('dist/img/about-me.jpg') }}" class="lazyload img-fluid"
                    alt="About Zahir Hayrullah">
            </div>
        </div>
        <div class="col-12 col-lg">
            <div class="text-wrapper">
                <h3 class="mbr-section-title mbr-fonts-style mb-3 display-5">
                    <strong>Who is <PERSON><PERSON><PERSON> ?</strong>
                </h3>
                <p class="mbr-text mbr-fonts-style display-7">I'm <PERSON><PERSON><PERSON> , <strong>backend web developer</strong>, I'm
                    from Syria, I've been living in Turkey since 2013, I love my job, I see my job as a hobby for me,
                    <br>I have fun with codes and I think it's characteristic of coding. By coding we can make all our
                    dreams come true, I love learning new things and following all the new technical news especially
                    when it comes to programming.&nbsp;<br>
                    <br>I like simple things, I just want to grab a cup of coffee and listen to some music and do some
                    simple coding to solve complex problems in this world.
                    <br>
                    <br>I work in <a href="https://www.imtilakgroup.com" rel="nofollow" target="_blank">IMTILAK
                        Group</a> is a group of twelve companies in many industries Real estate, Tourism, Medicine,
                    Education,&nbsp; Architecture,&nbsp; Wholesale and&nbsp;E-Commerce.
                </p>
            </div>
        </div>
    </div>
</section>

{{-- <section id="about" class="about-area"> --}}
{{--    <div class="d-flex justify-content-center"> --}}
{{--        <div class="justify-content-start"> --}}
{{--            <div class="about-image"> --}}
{{--                <img data-src="{{asset_theme_v('dist/img/about-me.png')}}" alt="About Me" class="lazyload img-fluid"> --}}
{{--            </div> --}}
{{--        </div> --}}
{{--        <div class="justify-content-end"> --}}
{{--            <div class="about-title"> --}}
{{--                <h2 class="text-uppercase pt-5 "> --}}
{{--                    <span>Let me</span> --}}
{{--                    <span>introduce</span> --}}
{{--                    <span>myself</span> --}}
{{--                    --}}{{--                        <span class="d-block d-sm-none d-md-none d-lg-none d-xl-none">Let me introduce myself</span> --}}
{{--                </h2> --}}

{{--                <div class="paragraph py-4 w-100"> --}}
{{--                    <p class="para"> --}}
{{--                        {!! $profile->bio !!} --}}
{{--                    </p> --}}
{{--                    --}}{{-- <p class="para"> --}}
{{--                    --}}{{--     Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature --}}
{{--                    --}}{{--     from 45 BC, making it over 2000 years old. </p> --}}
{{--                    --}}{{-- <p class="para"> --}}
{{--                    --}}{{--     It is a long established fact that a reader will be distracted by the readable content of a --}}
{{--                    --}}{{--     page when looking at its layout. The point of using Lorem Ipsum is that it has a --}}
{{--                    --}}{{--     more-or-less normal distribution of letters, as opposed to using 'Content here, content --}}
{{--                    --}}{{--     here', making it look like readable English. --}}
{{--                    --}}{{-- </p> --}}
{{--                </div> --}}
{{--                <div class="d-flex flex-row flex-wrap"> --}}
{{--                    @if (request()->url() != route('about_us')) --}}
{{--                        <a href="{{route('about_us')}}" class="btn button primary-button"><i class="fas fa-eye"></i> See More</a> --}}
{{--                    @endif --}}
{{--                </div> --}}
{{--            </div> --}}
{{--        </div> --}}
{{--    </div> --}}
{{-- </section> --}}
