@extends('themes.standard.old.layouts.red')

@section('content')
    <div id="tab-container" class="tab-container">
        <!-- Tab List -->
        <ul class='etabs'>
            <li class='tab' id="tab-about">
                <a href="#about"><i class="icon-user"></i><span> About Me</span></a>
            </li>
            <li class='tab'>
                <a href="#resume"><i class="icon-file-text"></i><span> Resume</span></a>
            </li>
            <li class='tab'>
                <a href="#projects"><i class="fa fa-cubes"></i><span> projects</span></a>
            </li>
            <!--<li class='tab'>
                     <a href="#portfolio"><i class="icon-camera"></i><span> Portfolio</span></a>
                 </li>-->
            <li class='tab'>
                <a href="#contact"><i class="icon-envelope"></i><span> Contact</span></a>
            </li>
        </ul>
        <!-- End Tab List -->
        <div id="tab-data-wrap">
            <!-- About Tab Data -->
            <div id="about">
                <section class="clearfix">
                    <div class="g2 social_item">
                        <div class="photo shadow">
                            <img src="{{ asset($user->profile->avatar) }} " alt="Sunny Luthra">
                        </div>
                        <div class="info ">
                            <h3>
                                {{ config('app.name', 'Zahir Hayrullah') }}
                            </h3>
                            {{-- {{ $user->profile->profession }} --}}
                            <p class="">{!! $user->profile->bio !!}</p>
                        </div>
                    </div>
                    <div class="g1">
                        <div class="main-links sidebar">
                            <h4 class="red-title"> {{ __('front.languages') }}</h4>
                            @if (count($user->languages) > 0)
                                <ul>
                                    @foreach ($user->languages as $language)
                                        <li class="edu_item text-center">
                                            {{ __("front.$language->name") }}
                                            <span class="lang-desc">
                                                <small> -- {{ __("front.$language->level") }} </small>
                                            </span>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </div>
                        {{-- <li>
                            <a href="#resume">View My Resume</a>
                        </li>
                        <li>
                            <a href="#portfolio">My Portfolio</a>
                        </li>
                        <li>
                            <a href="#contact">Hire me for your next project</a>
                        </li>
                        <li>
                            <a href="#features">Features</a>
                        </li> --}}

                    </div>
                    <div class="break"></div>
                    <div class="contact-info">
                        <div class="g1 social_item">
                            <div class="item-box clearfix">
                                <i class="icon-envelope"></i>
                                <div class="item-data">
                                    <h3><a href="mailto:{{ $user->profile->email }}"
                                            target="_blank">{{ $user->profile->email }}</a></h3>
                                    <p>Email Address</p>
                                </div>
                            </div>
                        </div>
                        <div class="g1 social_item">
                            <div class="item-box clearfix">
                                <i class="icon-globe"></i>
                                <div class="item-data">
                                    <h3><a href="{{ $user->profile->website }}"
                                            target="_blank">{{ $user->profile->website }}</a></h3>
                                    <p>Website</p>
                                </div>
                            </div>
                        </div>
                        @if (count($user->socials) > 0)
                            @foreach ($user->socials as $social)
                                <div class="g1 social_item">
                                    <div class="item-box clearfix">
                                        <i class="icon-{{ $social->class_icon }}"></i>
                                        <div class="item-data">
                                            <h3>{{ $social->name }}</h3>
                                            <p><a href="{{ $social->url }}" target="_blank">{{ $social->url }}</a></p>
                                            {{-- <p>{{ $social->slug }} profile</p> --}}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </section>
                <!-- content -->
            </div>
            <!-- End About Tab Data -->
            <div id="resume">
                <section class="clearfix">
                    <div class="g2 social_item" style="padding:15px;">
                        <h4 class="red-title">
                            {{ __('front.work_experience') }}
                        </h4>
                        @if (count($user->works) > 0)
                            <ul class="no-list work">
                                @foreach ($user->works as $work)
                                    <li>
                                        <h5>{{ $work->name }}
                                        </h5>
                                        <span class="label label-{{ $work->calss }} label-info">

                                            @php
                                                $start = \Carbon\Carbon::createFromFormat('Y-m-d', $work->started_at)
                                                    ->year;
                                                $finish = \Carbon\Carbon::createFromFormat('Y-m-d', $work->finshed_at)
                                                    ->year;
                                            @endphp
                                            @if ($finish == $start)
                                                {{ $finish }}
                                            @else
                                                {{ $start }} -
                                                @if ($work->finshed_at > Today())
                                                    continue
                                                @else
                                                    {{ $finish }}
                                                @endif
                                            @endif
                                        </span>
                                        <span class="g1">
                                            <small class=""><b> {{ $work->company_name }}</b> </small>
                                        </span>
                                        <p>{{ $work->description }}</p>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <div class="text-danger f-20">
                                <i class="fa fa-info-circle"></i>
                                No information about Work Experiences
                            </div>
                        @endif

                        <div style="color:#eee; margin-top:5%;"></div>
                        <h4 class="red-title">
                            {{ __('front.education') }}
                        </h4>
                        @if ($user->educations)
                            <ul class="no-list work ">
                                @foreach ($user->educations as $education)
                                    <li class="edu_item">
                                        <h5>{{ $education->id }}. {{ $education->name }}</h5>
                                        <span class="label label-{{ $education->class }} label-success">
                                        </span>
                                        <span class="label label-info">
                                            <i>{{ $education->department }}</i>
                                        </span>
                                        <p>
                                            <i class="fa fa-university"></i>
                                            {{ $education->school }}.
                                            <span class="label label-danger">
                                                <i class="fa fa-globe"></i>
                                                {{ $education->country }}
                                            </span>
                                        </p>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <div class="text-danger">
                                <i class="fa fa-info-circle"></i>
                                No information about Education
                            </div>
                        @endif
                    </div>
                    <div class="g1 social_item">
                        <div class="sidebar">
                            <h4 class="red-title">
                                {{ __('front.skills') }}
                            </h4>

                            @if (!empty($user->skills))
                                @foreach ($user->skills as $key => $skill)
                                    @if ($key == 0)
                                        @continue
                                    @endif
                                    @if (count($skill->items))
                                        <h5>
                                            {{ $skill->{'title_' . app()->getLocale()} }}
                                        </h5>
                                        @foreach ($skill->items as $item)
                                            <div class="meter {{ optional($skill->color)->name2 }}"
                                                title="{{ $item->description }}">
                                                <span style="width: {{ $item->level }}%">
                                                    <span> {{ $item->{'title_' . app()->getLocale()} }}</span>
                                                </span>
                                            </div>
                                        @endforeach
                                        <div class="break"></div>
                                    @endif
                                @endforeach
                            @endif

                            {{-- @if (count($skills) > 0)
                                @foreach ($skills as $skill)
                                <div class="meter {{ $skill->class }}">
                                    <span style="width: {{ $skill->level }}%"><span> {{ $skill->name }}</span></span>
                                </div>
                                @endforeach
                            @endif --}}
                        </div>
                    </div>
                    <div class="g1 social_item">
                        <div class="sidebar">
                            <h4 class="red-title">
                                {{ __('front.hobbies') }}
                            </h4>
                            @if (count($user->hobbies) > 0)
                                <ul class="no-list work">
                                    @foreach ($user->hobbies as $hobby)
                                        <li class="hob_item text-center">
                                            <b> {{ $hobby->name }}</b>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </div>
                    </div>
                </section>
            </div>
            <!-- End Resume Tab Data -->
            <div id="projects">
                <section class="clearfix">
                    @if (count($user->projects) > 0)
                        @foreach ($user->projects as $project)
                            <div class="g1">
                                <div class="image shad" style="margin-bottom:15px; ">
                                    <img src="{{ asset('red/assets/images/project_' . $project->id . '.png') }}"
                                        style="border-radius:5px;" height="275" alt="{{ $project->name }}">
                                    <div class="image-overlay">
                                        <div class="image-link">
                                            <a href="{{ $project->url }}" target="_blank" class="btn">
                                                {{ $project->name }} <i class="fa fa-search fa-2x"></i>
                                            </a>
                                            <div class="text-white">{{ $project->description }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                    <div class="break"></div>
                </section>
            </div>
            <!-- End projects Data -->
            <!-- <div id="portfolio">
                <section class="clearfix">
                    @if (count($user->images) > 0)
    @foreach ($user->images as $image)
    <div class="g1">
                        <div class="image shad" style="margin-bottom:15px; ">
                            <img src="{{ asset($image->path) }}" style="border-radius:5px;"height="275" alt="">
                            <div class="image-overlay">
                                <div class="image-link">
                                    <a href="{{ asset($image->path) }}" class="btn">
                                        <i class="fa fa-eye fa-2x"></i> {{ $image->name }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
    @endforeach
    @endif
                    <div class="break"></div>
                </section>
            </div>
    -->
            <!-- End Portfolio Data -->
            <div id="contact">
                <section class="clearfix">
                    <div class="g1">
                        <div class="sny-icon-box shadow">
                            <div class="sny-icon">
                                <i class="fa fa-globe"></i>
                            </div>
                            <div class="sny-icon-content">
                                <h4 class="red-title"> {{ __('front.my_address') }}</h4>
                                <p>{{ $user->profile->location }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="g1">
                        <div class="sny-icon-box shadow">
                            <div class="sny-icon">
                                <i class="fa fa-phone"></i>
                            </div>
                            <div class="sny-icon-content">
                                <h4 class="red-title"> {{ __('front.mobile_number') }} </h4>
                                <p>
                                    <a
                                        href="tel://{{ $user->profile->phone_number }}">+{{ $user->profile->phone_number }}</a>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="g1">
                        <div class="sny-icon-box shadow">
                            <div class="sny-icon">
                                <i class="fa fa-whatsapp"></i>
                            </div>
                            <div class="sny-icon-content">
                                <h4 class="red-title">whatsapp {{ __('front.number') }} </h4>
                                <a
                                    href="https://api.whatsapp.com/send?phone={{ $user->profile->phone_number }}&text=Hello...!">+{{ $user->profile->phone_number }}</a>
                            </div>
                        </div>
                    </div>
                    <div class="g3 social_item  col-md-12 ">
                        <form method="POST" action="{{ route('send.contact.message') }}">
                            @csrf
                            <div class="form-group row">
                                <div class="col-md-6">
                                    <input id="name" type="text"
                                        class="form-control{{ $errors->has('name') ? ' is-invalid' : '' }}"
                                        name="name" placeholder="Name *" value="{{ old('name') }}" required
                                        autofocus>
                                    @if ($errors->has('name'))
                                        <span class="invalid-feedback">
                                            <strong>{{ $errors->first('name') }}</strong>
                                        </span>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <input id="email" type="email"
                                        class="form-control{{ $errors->has('email') ? ' is-invalid' : '' }}"
                                        name="email" placeholder="Email *" value="{{ old('email') }}" required
                                        autofocus>
                                    @if ($errors->has('email'))
                                        <span class="invalid-feedback">
                                            <strong>{{ $errors->first('email') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-12">
                                    <input id="subject" type="text"
                                        class="form-control{{ $errors->has('subject') ? ' is-invalid' : '' }}"
                                        name="subject" placeholder="subject *" value="{{ old('subject') }}">
                                    @if ($errors->has('subject'))
                                        <span class="invalid-feedback">
                                            <strong>{{ $errors->first('subject') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-12">
                                    <textarea id="message" type="text" class="form-control{{ $errors->has('message') ? ' is-invalid' : '' }}"
                                        name="message" placeholder="Message *" value="{{ old('message') }}" required autofocus></textarea>
                                    @if ($errors->has('message'))
                                        <span class="invalid-feedback">
                                            <strong>{{ $errors->first('message') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-lg btn-primary">
                                    Send <span></span> <span></span> <i class="fa fa-envelope"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="break"></div>
                </section>
            </div>
            <!-- End Contact Data -->
        </div>
    </div>
@endsection
