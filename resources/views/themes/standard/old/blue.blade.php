@extends('themes.standard.old.layouts.blue')

@section('content')
    <style>
        .profile-container img {
            width: 100%;
        }

        .radius5 {
            border-radius: 5%;
        }

        .shadow-white {
            box-shadow: 1px 15px 20px -7px rgba(0, 0, 0, 0.5);
            transition: all 0.3s;
        }

        .shadow-white:hover {
            box-shadow: 1px 20px 20px -5px rgba(0, 0, 0, 0.8);
            transition: all 0.3s;
        }

        .circle {
            border-radius: 50%;
        }


        .shadow {
            box-shadow: 1px 10px 20px -7px rgba(50, 50, 50, 0.3);
            transition: all 0.3s;
        }

        .shadow:hover {
            box-shadow: 1px 12px 20px -5px rgba(50, 50, 50, 0.5);
            transition: all 0.3s;
        }

        .zoom {
            transition: all 0.5s;
            margin: -1px -1px;
        }

        .f-20 {
            font-size: 20px;
        }

        .zoom:hover {
            transform: scale(1.3);
            border-radius: 0;
            transition: all 0.5s;
        }

        .title_name {
            transition: all 0.5s;
            margin: 12px 0;
            transform: scale(1.3);
        }

        .contact-list a:hover {
            margin: 5px -5px;
            transition: all 0.3s;
            font-size: 15px;
        }

        .zoom:hover+.title_name {
            transition: all 0.5s;
            color: #159;
        }

        .main-wrapper {
            box-shadow: -5px 15px 30px -10px rgba(50, 50, 50, 0.3);
            border-radius: 5%;
        }
    </style>
    <div class="sidebar-wrapper">
        <div class="profile-container">
            <img class="zoom radius5 shadow-white" src="{{ asset('old/assets/images/profile.png') }} " alt="" />
            <h4 class="title_name">Zahir Hayrullah</h4>
            <h3 class="tagline"> {{ $user->profile->profession }}</h3>
        </div>
        <!--/profile-container-->
        @if ($user->profile)
            <div class="contact-container container-block">
                <ul class="list-unstyled contact-list">
                    <li class="email">
                        <i class="fa fa-envelope"></i>
                        <a href="mailto: {{ $user->profile->email }} ">
                            <small>{{ $user->profile->email }}</small>
                        </a>
                    </li>
                    <li class="phone">
                        <i class="fa fa-phone"></i>
                        <a href="tel:{{ $user->profile->phone_number }}">
                            <small>{{ $user->profile->phone_number }}</small>
                        </a>
                    </li>
                    <li class="website">
                        <i class="fa fa-globe"></i>
                        <a href="{{ $user->profile->website }}" target="_blank">
                            <small>{{ $user->profile->website }}</small>
                        </a>
                    </li>
                    @if (count($user->socials) > 0)
                        @foreach ($user->socials as $social)
                            <li class="linkedin">
                                <i class="fa fa-{{ $social->class_icon }}"></i>
                                <a href="{{ $social->url }}" target="_blank">{{ $social->name }}</a>
                            </li>
                        @endforeach
                    @endif
                </ul>
            </div>
        @else
            <div class="text-danger">No information</div>
        @endif
        <!--/contact-contain-->
        <div class="education-container container-block">
            <h2 class="container-block-title">Education</h2>
            @if (count($user->educations) > 0)
                @foreach ($user->educations->take(3) as $education)
                    <div class="item">
                        <h4 class="degree">{{ $education->name }}</h4>
                        <p class="meta">{{ $education->school }}</p>
                        <span class="time" style="color:#EEE;">
                            <i class="fa fa-calendar"></i>
                            @php
                                $start = \Carbon\Carbon::createFromFormat('Y-m-d', $education->started_at)->year;
                                $finish = \Carbon\Carbon::createFromFormat('Y-m-d', $education->finshed_at)->year;
                            @endphp
                            @if ($finish == $start)
                                {{ $finish }}
                            @else
                                {{ $start }} -
                                @if ($education->finshed_at > Today())
                                    Continue
                                @else
                                    {{ $finish }}
                                @endif
                            @endif

                            <i class="fa fa-globe"></i> {{ $education->country }}
                        </span>
                    </div>
                    <!--/item-->
                @endforeach
            @else
                <div class="text-danger">Done't have any Education</div>
            @endif
        </div>
        <!--/education-container-->
        <div class="languages-container container-block">
            <h2 class="container-block-title">Languages</h2>

            @if (count($user->languages) > 0)
                <ul class="list-unstyled interests-list">
                    @foreach ($user->languages as $language)
                        <li>
                            @lang('lang.' . $language->name)
                            <span class="lang-desc">
                                <small> -- @lang('lang.' . $language->level)</small>
                            </span>
                        </li>
                    @endforeach
                </ul>
            @else
                <div class="text-danger">Don't know any language</div>
            @endif
        </div>
        <!--/languages-->
        {{--
    <div class="interests-container container-block">
        <h2 class="container-block-title">Interests</h2>
        @if (count($user->hobbies) > 0)
        <ul class="list-unstyled interests-list">
        @foreach ($user->hobbies as $hobie)
            <li>{{ $hobie->name }}</li>
        @endforeach
        </ul>
        @endif
    </div>
    --}}
        <!--/interests-->
    </div>

    <!--/sidebar-wrapper-->
    <div class="main-wrapper">

        <section class="section summary-section">
            <h2 class="section-title">
                <i class="fa fa-user"></i>
                Career Profile
            </h2>
            <div class="summary">
                <p>{!! $user->profile->bio !!} </p>
            </div><!--//summary-->
        </section>
        <!--//jobs section-->
        <section class="section experiences-section">
            <h2 class="section-title">
                <i class="fa fa-briefcase"></i>Experiences
            </h2>
            @if (count($user->works) > 0)
                @foreach ($user->works as $job)
                    <div class="item">
                        <div class="meta">
                            <div class="upper-row">
                                <h3 class="job-title">{{ $job->name }}</h3>
                                <div class="time">{{ $job->started_at }} - Present</div>
                            </div><!--//upper-row-->
                            <div class="company">{{ $job->company_name }}, San Francisco</div>
                        </div><!--//meta-->
                        <div class="details">
                            <p>{{ $job->description }}.</p>
                            {{-- <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. </p> --}}
                        </div><!--//details-->
                    </div><!--//item-->
                @endforeach
            @else
                <div class="text-danger text-center f-20">
                    <i class="fa fa-info-circle"></i>
                    No information about Work Experiences
                </div>
            @endif
        </section>

        <!--//section-->
        <section class="section projects-section">
            <h2 class="section-title">
                <i class="fa fa-archive"></i> Projects
            </h2>
            <div class="intro">
                <p> This information about some of my projects</p>
            </div><!--//intro-->
            @if (count($user->projects) > 0)
                @foreach ($user->projects as $project)
                    <div class="item">
                        <span class="project-title">
                            <a href="@if ($project->url) {{ $project->url }}@else # @endif" target="_blank">
                                {{ $project->name }}
                            </a>
                        </span> :
                        <span class="project-tagline"> {{ $project->description }}. </span>
                    </div>
                    <!--/item-->
                @endforeach
            @endif
        </section>
        <!--//section-->
        <section class="skills-section section">
            <h2 class="section-title"><i class="fa fa-rocket"></i>Skills &amp; Proficiency</h2>
            @if (count($user->skills) > 0)
                <div class="skillset">
                    @foreach ($user->skills as $skill)
                        <div class="item">
                            <h3 class="level-title">{{ $skill->name }}</h3>
                            <div class="level-bar">
                                <div class="level-bar-inner" data-level="{{ $skill->level }}%">
                                </div>
                            </div><!--//level-bar-->
                        </div><!--//item-->
                    @endforeach

                </div>
            @endif
        </section>
        <!--//skills-section-->
        <section class="interests-section section">
            <h2 class="section-title">
                <i class="fa fa-rocket"></i>hobbies
            </h2>
            @if (count($user->hobbies) > 0)
                <div class="skillset">
                    @foreach ($user->hobbies as $hobie)
                        <div class="item">
                            <h3 class="level-title">{{ $hobie->name }}</h3>
                        </div><!--//item-->
                    @endforeach

                </div>
            @endif
        </section>

    </div>
    <!--/main-body-->
@endsection
