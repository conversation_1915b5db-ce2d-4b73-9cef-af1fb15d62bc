<head>
    <title>kross - Free Bootstrap 4 Template by Colorlib</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    @include('themes.kross.layout.partials.meta')
    <link rel="icon" type="image/x-icon" href="{{ asset_theme_v('dist/img/favicon.ico') }}">
    <link rel="canonical" href="{{ url()->current() }}" />
    <link rel="amphtml" href="@yield('amp_url', '/amp')" />

    <title> @yield('title', 'Web developer') || {{ get_setting('site_name') }}</title>

    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" rel="stylesheet">

    <link rel="stylesheet" href="{{ asset_theme_v('css/open-iconic-bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/animate.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/owl.carousel.min.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/owl.theme.default.min.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/magnific-popup.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/aos.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/ionicons.min.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/flaticon.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/icomoon.css') }}">
    <link rel="stylesheet" href="{{ asset_theme_v('css/style.css') }}">

    <!-- ** Plugins Needed for the Project ** -->
    <!-- Bootstrap -->
    <link rel="stylesheet" href="{{ asset_theme_v('plugins/bootstrap/bootstrap.min.css') }}">
    <!-- slick slider -->
    <link rel="stylesheet" href="{{ asset_theme_v('plugins/slick/slick.css') }}">
    <!-- themefy-icon -->
    <link rel="stylesheet" href="{{ asset_theme_v('plugins/themify-icons/themify-icons.css') }}">

    <!-- Main Stylesheet -->
    <link href="{{ asset_theme_v('css/style.css') }}" rel="stylesheet">

    @yield('styles')
    {!! CorporationSchema() !!}
    @yield('schema')

    @include('themes.kross.layout.partials.seo_scripts')
</head>
