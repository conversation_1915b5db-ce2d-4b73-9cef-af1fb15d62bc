<section class="section section-on-footer" data-background="{{ asset_theme_v('images/backgrounds/bg-dots.png') }}">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h2 class="section-title">Contact Info</h2>
            </div>
            <div class="col-lg-8 mx-auto">
                <div class="bg-white rounded text-center p-5 shadow-down">
                    @include('themes.kross.layout.partials.flash_message')
                    <h4 class="mb-80">Contact Form</h4>
                    <form action="{{ route('post_contact') }}" method="post" class="row">
                        @csrf
                        <div class="col-md-6">
                            <input type="text" id="name" name="name" {{ old('name') }}
                                placeholder="{{ __trans('Name') }}" class="form-control px-0 mb-4">
                            @error('name')
                                <span class="error text-danger">{{ $message }}</span>
                            @enderror

                        </div>
                        <div class="col-md-6">
                            <input type="email" id="email" name="email" {{ old('email') }}
                                placeholder="{{ __trans('Email Address') }}" class="form-control px-0 mb-4">
                            @error('email')
                                <span class="error text-danger">{{ $message }}</span>
                            @enderror

                        </div>
                        <div class="col-12">
                            <input type="text" id="subject" name="subject" {{ old('subject') }}
                                placeholder="{{ __trans('Subject') }}" class="form-control px-0 mb-4">
                            @error('subject')
                                <span class="error text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-12">
                            <textarea name="message" id="message" class="form-control px-0 mb-4" placeholder="{{ __('Type Message Here') }}">{{ old('message') }}</textarea>
                            @error('message')
                                <span class="error text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                        <div class="col-lg-6 col-10 mx-auto">
                            <button class="btn btn-primary w-100">send</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
