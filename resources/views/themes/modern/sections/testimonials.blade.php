<section id="testimonials" class="section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">

            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">What Clients Say</h2>
                <p class="section-subtitle">
                    Don't just take my word for it - here's what my clients have to say about working with me
                </p>
            </div>

            <!-- Testimonials Slider -->
            <div class="relative" data-animate>
                <div class="testimonials-slider overflow-hidden">
                    <div class="testimonials-track flex transition-transform duration-500 ease-in-out">

                        @php
                            $testimonials = [
                                [
                                    'name' => '<PERSON>',
                                    'position' => 'CEO, TechStart Inc.',
                                    'avatar' => 'images/testimonials/client-1.jpg',
                                    'rating' => 5,
                                    'content' =>
                                        'Working with this developer was an absolute pleasure. The attention to detail and technical expertise exceeded our expectations. Our project was delivered on time and within budget.',
                                ],
                                [
                                    'name' => '<PERSON>',
                                    'position' => 'Product Manager, Digital Solutions',
                                    'avatar' => 'images/testimonials/client-2.jpg',
                                    'rating' => 5,
                                    'content' =>
                                        'Exceptional work quality and great communication throughout the project. The final product was exactly what we envisioned, and the ongoing support has been fantastic.',
                                ],
                                [
                                    'name' => 'Emily Rodriguez',
                                    'position' => 'Founder, Creative Agency',
                                    'avatar' => 'images/testimonials/client-3.jpg',
                                    'rating' => 5,
                                    'content' =>
                                        'Professional, reliable, and incredibly skilled. This developer transformed our ideas into a beautiful, functional website that has significantly improved our business.',
                                ],
                                [
                                    'name' => 'David Thompson',
                                    'position' => 'CTO, Innovation Labs',
                                    'avatar' => 'images/testimonials/client-4.jpg',
                                    'rating' => 5,
                                    'content' =>
                                        'Outstanding technical skills and problem-solving abilities. The developer was able to tackle complex challenges and deliver solutions that perfectly met our requirements.',
                                ],
                            ];
                        @endphp

                        @foreach ($testimonials as $testimonial)
                            <div class="testimonial-slide flex-shrink-0 w-full md:w-1/2 lg:w-1/3 px-4">
                                <div
                                    class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 h-full">

                                    <!-- Rating -->
                                    <div class="flex items-center mb-4">
                                        @for ($i = 1; $i <= 5; $i++)
                                            <svg class="w-5 h-5 {{ $i <= $testimonial['rating'] ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600' }}"
                                                fill="currentColor" viewBox="0 0 20 20">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                                </path>
                                            </svg>
                                        @endfor
                                    </div>

                                    <!-- Content -->
                                    <blockquote class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                                        "{{ $testimonial['content'] }}"
                                    </blockquote>

                                    <!-- Client Info -->
                                    <div class="flex items-center">
                                        <div
                                            class="w-12 h-12 bg-gradient-to-br from-primary-500 to-purple-500 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                            <span class="text-white font-bold text-lg">
                                                {{ substr($testimonial['name'], 0, 1) }}
                                            </span>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-900 dark:text-white">
                                                {{ $testimonial['name'] }}</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                                {{ $testimonial['position'] }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <button
                    class="testimonials-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 z-10"
                    aria-label="Previous testimonial" title="Previous testimonial">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7">
                        </path>
                    </svg>
                </button>

                <button
                    class="testimonials-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 w-12 h-12 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 z-10"
                    aria-label="Next testimonial" title="Next testimonial">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Dots Indicator -->
                <div class="flex justify-center mt-8 space-x-2">
                    @foreach ($testimonials as $index => $testimonial)
                        <button
                            class="testimonial-dot w-4 h-4 p-2 rounded-full transition-colors duration-200 {{ $index === 0 ? 'bg-primary-600' : 'bg-gray-300 dark:bg-gray-600' }}"
                            style="min-width: 44px; min-height: 44px;" data-slide="{{ $index }}"
                            aria-label="Go to testimonial {{ $index + 1 }}"
                            title="Testimonial {{ $index + 1 }}"></button>
                    @endforeach
                </div>
            </div>

            <!-- CTA Section -->
            <div class="text-center mt-16" data-animate>
                <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8 md:p-12">
                    <h3 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                        Ready to Join These Happy Clients?
                    </h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                        Let's discuss your project and see how I can help you achieve your goals with the same level of
                        excellence and dedication.
                    </p>
                    <a href="#contact"
                        class="inline-flex items-center px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        <span>Start Your Project</span>
                        <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    // Testimonials slider functionality
    document.addEventListener('DOMContentLoaded', function() {
        const track = document.querySelector('.testimonials-track');
        const slides = document.querySelectorAll('.testimonial-slide');
        const prevBtn = document.querySelector('.testimonials-prev');
        const nextBtn = document.querySelector('.testimonials-next');
        const dots = document.querySelectorAll('.testimonial-dot');

        let currentSlide = 0;
        const totalSlides = slides.length;
        let slidesToShow = 1;

        // Determine slides to show based on screen size
        function updateSlidesToShow() {
            if (window.innerWidth >= 1024) {
                slidesToShow = 3;
            } else if (window.innerWidth >= 768) {
                slidesToShow = 2;
            } else {
                slidesToShow = 1;
            }
        }

        // Update slider position
        function updateSlider() {
            const translateX = -(currentSlide * (100 / slidesToShow));
            track.style.transform = `translateX(${translateX}%)`;

            // Update dots
            dots.forEach((dot, index) => {
                dot.classList.toggle('bg-primary-600', index === currentSlide);
                dot.classList.toggle('bg-gray-300', index !== currentSlide);
                dot.classList.toggle('dark:bg-gray-600', index !== currentSlide);
            });
        }

        // Next slide
        function nextSlide() {
            currentSlide = (currentSlide + 1) % Math.max(1, totalSlides - slidesToShow + 1);
            updateSlider();
        }

        // Previous slide
        function prevSlide() {
            currentSlide = currentSlide === 0 ? Math.max(0, totalSlides - slidesToShow) : currentSlide - 1;
            updateSlider();
        }

        // Event listeners
        nextBtn.addEventListener('click', nextSlide);
        prevBtn.addEventListener('click', prevSlide);

        // Dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                updateSlider();
            });
        });

        // Auto-play
        let autoplayInterval = setInterval(nextSlide, 5000);

        // Pause autoplay on hover
        const slider = document.querySelector('.testimonials-slider');
        slider.addEventListener('mouseenter', () => {
            clearInterval(autoplayInterval);
        });

        slider.addEventListener('mouseleave', () => {
            autoplayInterval = setInterval(nextSlide, 5000);
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            updateSlidesToShow();
            updateSlider();
        });

        // Initialize
        updateSlidesToShow();
        updateSlider();
    });
</script>
