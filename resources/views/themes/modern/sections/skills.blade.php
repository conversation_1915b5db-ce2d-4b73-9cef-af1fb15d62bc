@php
    $skillsConfig = theme_config_get('skills', []);
    $skillsCategories = get_skills_by_category();
    $softSkills = get_soft_skills();
@endphp

@push('styles')
    <style>
        /* Technical Skills Grid Responsive Styles */
        .skills-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        /* Tablet and up: 2x2 grid */
        @media (min-width: 1024px) {
            .skills-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Ensure cards have equal height */
        .skills-card {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .skills-card-content {
            flex: 1;
        }

        /* Category icon colors */
        .category-icon-backend {
            color: #3b82f6;
        }

        .category-icon-frontend {
            color: #10b981;
        }

        .category-icon-database {
            color: #8b5cf6;
        }

        .category-icon-tools {
            color: #f59e0b;
        }

        /* Dark mode category icon colors */
        .dark .category-icon-backend {
            color: #60a5fa;
        }

        .dark .category-icon-frontend {
            color: #34d399;
        }

        .dark .category-icon-database {
            color: #a78bfa;
        }

        .dark .category-icon-tools {
            color: #fbbf24;
        }
    </style>
@endpush

<section id="skills" class="section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="max-w-6xl mx-auto">

            <!-- Section Header -->
            <div class="text-center mb-16" data-animate>
                <h2 class="section-title">{{ $skillsConfig['title'] ?? 'Skills & Expertise' }}</h2>
                <p class="section-subtitle">
                    {{ $skillsConfig['subtitle'] ?? 'Technologies and tools I work with to create amazing digital experiences' }}
                </p>
                @if (!empty($skillsConfig['description']))
                    <p class="text-gray-600 dark:text-gray-300 mt-4 max-w-3xl mx-auto">
                        {{ $skillsConfig['description'] }}
                    </p>
                @endif
            </div>

            <!-- Professional Skills Section -->
            @if ($skillsConfig['show_soft_skills'] ?? true && !empty($softSkills))
                <div data-animate class="mb-16">
                    <div
                        class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Professional Skills</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach ($softSkills as $skill)
                                <div
                                    class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-300">
                                    <div
                                        class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-3">
                                        <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                    <h4 class="font-medium text-gray-900 dark:text-white text-sm mb-2">
                                        {{ $skill['name'] }}</h4>
                                    @if (!empty($skill['description']))
                                        <p class="text-xs text-gray-600 dark:text-gray-400">
                                            {{ $skill['description'] }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Technical Skills Categories - 2x2 Grid Layout -->
            @if (!empty($skillsCategories))
                @php
                    // Define the order of categories for 2x2 grid
                    $orderedCategories = ['backend', 'frontend', 'database', 'tools'];
                    $gridCategories = [];

                    // Organize categories in the specified order
                    foreach ($orderedCategories as $key) {
                        if (isset($skillsCategories[$key])) {
                            $gridCategories[$key] = $skillsCategories[$key];
                        }
                    }
                @endphp

                <div class="skills-grid" data-animate>
                    @foreach ($gridCategories as $categoryKey => $category)
                        <div
                            class="skills-card bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-shadow duration-300">
                            <div class="skills-card-content">
                                <div class="mb-6">
                                    <div class="flex items-center mb-4">
                                        @php
                                            $categoryIcons = [
                                                'backend' => [
                                                    'icon' =>
                                                        '<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12l4-4m-4 4l4 4"></path></svg>',
                                                    'class' => 'category-icon-backend',
                                                ],
                                                'frontend' => [
                                                    'icon' =>
                                                        '<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>',
                                                    'class' => 'category-icon-frontend',
                                                ],
                                                'database' => [
                                                    'icon' =>
                                                        '<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path></svg>',
                                                    'class' => 'category-icon-database',
                                                ],
                                                'tools' => [
                                                    'icon' =>
                                                        '<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
                                                    'class' => 'category-icon-tools',
                                                ],
                                            ];
                                        @endphp
                                        <div
                                            class="mr-4 {{ $categoryIcons[$categoryKey]['class'] ?? 'text-gray-600 dark:text-gray-400' }}">
                                            {!! $categoryIcons[$categoryKey]['icon'] ??
                                                '<svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>' !!}
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                                                {{ $category['title'] }}</h3>
                                            @if (!empty($category['description']))
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                    {{ $category['description'] }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    @foreach ($category['skills'] as $skill)
                                        <div class="flex items-center space-x-4">
                                            @if (!empty($skill['icon']))
                                                @php $iconPath = get_skill_icon_path($skill['icon']); @endphp
                                                @if ($iconPath)
                                                    <img src="{{ $iconPath }}" alt="{{ $skill['name'] }}"
                                                        class="w-8 h-8 flex-shrink-0">
                                                @else
                                                    <div
                                                        class="w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center flex-shrink-0">
                                                        <span
                                                            class="text-primary-600 dark:text-primary-400 text-xs font-bold">{{ substr($skill['name'], 0, 2) }}</span>
                                                    </div>
                                                @endif
                                            @endif

                                            <div class="flex-1 min-w-0">
                                                <div class="flex justify-between items-center mb-2">
                                                    <span
                                                        class="text-gray-700 dark:text-gray-300 font-medium">{{ $skill['name'] }}</span>
                                                    @if ($skillsConfig['show_proficiency_levels'] ?? true)
                                                        <div class="flex items-center space-x-2">
                                                            @if (!empty($skill['years']))
                                                                <span
                                                                    class="text-xs text-gray-500 dark:text-gray-400">{{ $skill['years'] }}y</span>
                                                            @endif
                                                            <span
                                                                class="text-primary-600 dark:text-primary-400 font-medium text-sm">{{ $skill['level'] }}%</span>
                                                        </div>
                                                    @endif
                                                </div>
                                                @if ($skillsConfig['show_proficiency_levels'] ?? true)
                                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                                        <div class="bg-gradient-to-r from-primary-600 to-purple-600 h-2 rounded-full transition-all duration-1000 ease-out"
                                                            style="width: {{ $skill['level'] }}%"></div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">No technical skills data available.</p>
                </div>
            @endif


        </div>
    </div>
    </div>
</section>
