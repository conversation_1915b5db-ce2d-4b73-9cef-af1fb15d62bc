<header
    class="fixed top-0 left-0 right-0 z-40 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 transition-all duration-300"
    x-data="navbar()" :class="{ 'shadow-lg': scrolled }" x-init="init()">
    <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16 md:h-20">

            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="{{ url('/') }}"
                    class="flex items-center space-x-2 text-xl font-bold text-gray-900 dark:text-white hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
                    aria-label="Home">
                    @if (theme_config_get('logo'))
                        <img src="{{ theme_asset(theme_config_get('logo')) }}" alt="{{ config('app.name') }}"
                            class="h-8 w-auto">
                    @else
                        <span class="bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent">
                            {{ config('app.name', 'Portfolio') }}
                        </span>
                    @endif
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:block">
                <div class="ml-10 flex items-baseline space-x-6">
                    <a href="{{ url('/') }}"
                        class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->is('/') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        Home
                    </a>
                    <a href="{{ url('/about-me') }}"
                        class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->is('about-me') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        About
                    </a>
                    <a href="{{ url('/skills') }}"
                        class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->is('skills*') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        Skills
                    </a>
                    <a href="{{ url('/experience') }}"
                        class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->is('experience*') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        Experience
                    </a>
                    <a href="{{ url('/services') }}"
                        class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->is('services*') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        Services
                    </a>
                    <a href="{{ url('/education') }}"
                        class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->is('education*') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        Education
                    </a>
                    <a href="{{ url('/contact-me') }}"
                        class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->is('contact-me') ? 'text-primary-600 dark:text-primary-400 font-semibold' : '' }}">
                        Contact
                    </a>
                </div>
            </div>

            <!-- Desktop Actions -->
            <div class="hidden md:flex items-center space-x-4">

                <!-- Theme Toggle -->
                @include('themes.modern.partials.theme-toggle')

                <!-- Language Switcher -->
                @if (count(config('app.available_locales', [])) > 1)
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                            class="flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200"
                            aria-label="Change language">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
                                </path>
                            </svg>
                            <span class="text-sm">{{ strtoupper(app()->getLocale()) }}</span>
                        </button>

                        <div x-show="open" @click.away="open = false"
                            x-transition:enter="transition ease-out duration-200"
                            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-75"
                            x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95"
                            class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50">
                            @foreach (config('app.available_locales', []) as $locale => $name)
                                <a href="{{ route('locale.switch', $locale) }}"
                                    class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    {{ $name }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- CTA Button -->
                <a href="{{ url('/contact-me') }}"
                    class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    Get In Touch
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden">
                <button @click="mobileMenuOpen = !mobileMenuOpen"
                    class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200"
                    aria-label="Toggle mobile menu">
                    <svg class="h-6 w-6" :class="{ 'hidden': mobileMenuOpen, 'block': !mobileMenuOpen }" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <svg class="h-6 w-6" :class="{ 'block': mobileMenuOpen, 'hidden': !mobileMenuOpen }" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="md:hidden" x-show="mobileMenuOpen" x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-100" x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-95">
            <div
                class="px-2 pt-2 pb-3 space-y-1 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ url('/') }}"
                    class="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200 {{ request()->is('/') ? 'text-primary-600 dark:text-primary-400 bg-gray-50 dark:bg-gray-800' : '' }}"
                    @click="mobileMenuOpen = false">
                    Home
                </a>
                <a href="{{ url('/about-me') }}"
                    class="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200 {{ request()->is('about-me') ? 'text-primary-600 dark:text-primary-400 bg-gray-50 dark:bg-gray-800' : '' }}"
                    @click="mobileMenuOpen = false">
                    About
                </a>
                <a href="{{ url('/skills') }}"
                    class="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200 {{ request()->is('skills*') ? 'text-primary-600 dark:text-primary-400 bg-gray-50 dark:bg-gray-800' : '' }}"
                    @click="mobileMenuOpen = false">
                    Skills
                </a>
                <a href="{{ url('/experience') }}"
                    class="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200 {{ request()->is('experience*') ? 'text-primary-600 dark:text-primary-400 bg-gray-50 dark:bg-gray-800' : '' }}"
                    @click="mobileMenuOpen = false">
                    Experience
                </a>
                <a href="{{ url('/services') }}"
                    class="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200 {{ request()->is('services*') ? 'text-primary-600 dark:text-primary-400 bg-gray-50 dark:bg-gray-800' : '' }}"
                    @click="mobileMenuOpen = false">
                    Services
                </a>
                <a href="{{ url('/education') }}"
                    class="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200 {{ request()->is('education*') ? 'text-primary-600 dark:text-primary-400 bg-gray-50 dark:bg-gray-800' : '' }}"
                    @click="mobileMenuOpen = false">
                    Education
                </a>
                <a href="{{ url('/contact-me') }}"
                    class="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md transition-colors duration-200 {{ request()->is('contact-me') ? 'text-primary-600 dark:text-primary-400 bg-gray-50 dark:bg-gray-800' : '' }}"
                    @click="mobileMenuOpen = false">
                    Contact
                </a>

                <!-- Mobile CTA -->
                <div class="pt-4 pb-2">
                    <a href="{{ url('/contact-me') }}"
                        class="block w-full text-center bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-base font-medium transition-colors duration-200"
                        @click="mobileMenuOpen = false">
                        Get In Touch
                    </a>
                </div>
            </div>
        </div>
    </nav>
</header>
