@extends('themes.modern.layout')

@section('title', $seoData['title'])
@section('description', $seoData['description'])
@section('keywords', $seoData['keywords'])

@push('styles')
    <style>
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3b82f6, #1d4ed8);
        }

        .timeline-item:last-child::before {
            background: linear-gradient(to bottom, #3b82f6, transparent);
        }

        .timeline-dot {
            position: absolute;
            left: 7px;
            top: 24px;
            width: 18px;
            height: 18px;
            background: #3b82f6;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .section-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .filter-badge {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* Custom background colors for section icons */
        .bg-green-600 {
            background-color: #059669;
        }

        .bg-purple-600 {
            background-color: #9333ea;
        }

        /* Hover states for the custom colors */
        .hover\:bg-green-700:hover {
            background-color: #047857;
        }

        .hover\:bg-purple-700:hover {
            background-color: #7c3aed;
        }

        /* Dark mode variants */
        .dark .bg-green-600 {
            background-color: #059669;
        }

        .dark .bg-purple-600 {
            background-color: #9333ea;
        }

        /* Additional color variants used in the page */
        .text-green-600 {
            color: #059669;
        }

        .text-green-400 {
            color: #34d399;
        }

        .text-purple-600 {
            color: #9333ea;
        }

        .text-purple-400 {
            color: #c084fc;
        }

        .bg-green-100 {
            background-color: #dcfce7;
        }

        .bg-green-900 {
            background-color: #14532d;
        }

        .text-green-800 {
            color: #166534;
        }

        .text-green-200 {
            color: #bbf7d0;
        }

        /* Dark mode text variants */
        .dark .text-green-600 {
            color: #059669;
        }

        .dark .text-green-400 {
            color: #34d399;
        }

        .dark .text-purple-600 {
            color: #9333ea;
        }

        .dark .text-purple-400 {
            color: #c084fc;
        }

        .dark .bg-green-100 {
            background-color: #14532d;
        }

        .dark .bg-green-900 {
            background-color: #14532d;
        }

        .dark .text-green-800 {
            color: #bbf7d0;
        }

        .dark .text-green-200 {
            color: #bbf7d0;
        }

        /* Sidebar Navigation Styles */
        .sidebar-nav {
            position: sticky;
            top: 2rem;
            max-height: calc(100vh - 4rem);
            overflow-y: auto;
        }

        .sidebar-nav::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-nav::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar-nav::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 2px;
        }

        .sidebar-nav::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.5);
        }

        .nav-item {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-item:hover {
            background-color: rgba(59, 130, 246, 0.05);
            border-left-color: rgba(59, 130, 246, 0.3);
        }

        .nav-item.active {
            background-color: rgba(59, 130, 246, 0.1);
            border-left-color: #3b82f6;
        }

        .nav-item.active .nav-link {
            color: #3b82f6;
            font-weight: 600;
        }

        .dark .nav-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .dark .nav-item.active {
            background-color: rgba(59, 130, 246, 0.15);
        }

        .dark .nav-item.active .nav-link {
            color: #60a5fa;
        }

        .nav-section-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        .nav-subsection {
            margin-left: 2rem;
            padding-left: 1rem;
            border-left: 1px solid rgba(156, 163, 175, 0.3);
        }

        .dark .nav-subsection {
            border-left-color: rgba(75, 85, 99, 0.5);
        }

        /* Mobile sidebar toggle */
        .mobile-nav-toggle {
            position: fixed;
            top: 50%;
            left: 1rem;
            transform: translateY(-50%);
            z-index: 1000;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 50%;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        .mobile-nav-toggle:hover {
            background: #2563eb;
            transform: translateY(-50%) scale(1.05);
        }

        .mobile-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .mobile-sidebar.active {
            opacity: 1;
            visibility: visible;
        }

        .mobile-sidebar-content {
            position: absolute;
            top: 0;
            left: 0;
            width: 80%;
            max-width: 320px;
            height: 100%;
            background: white;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .dark .mobile-sidebar-content {
            background: #1f2937;
        }

        .mobile-sidebar.active .mobile-sidebar-content {
            transform: translateX(0);
        }

        /* Responsive layout */
        @media (max-width: 1023px) {
            .desktop-sidebar {
                display: none;
            }
        }

        @media (min-width: 1024px) {
            .mobile-nav-toggle {
                display: none;
            }
        }

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Section spacing for proper navigation */
        .resume-section {
            scroll-margin-top: 2rem;
        }
    </style>
@endpush

@section('content')
    <!-- Hero Section -->
    <section
        class="mb-16 relative bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 py-20">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                    Professional Resume
                </h1>
                <p class="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
                    Comprehensive overview of my professional journey, educational background, and certifications spanning
                    {{ $totalExperience['years'] }}+ years in web development.
                </p>

                <!-- Quick Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                        <div class="text-3xl font-bold text-primary-600 dark:text-primary-400">
                            {{ $totalExperience['years'] }}+</div>
                        <div class="text-gray-600 dark:text-gray-300">Years Experience</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                        <div class="text-3xl font-bold text-primary-600 dark:text-primary-400">{{ $experiences->count() }}
                        </div>
                        <div class="text-gray-600 dark:text-gray-300">Professional Roles</div>
                    </div>
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                        <div class="text-3xl font-bold text-primary-600 dark:text-primary-400">
                            {{ $certifications->count() }}</div>
                        <div class="text-gray-600 dark:text-gray-300">Certifications</div>
                    </div>
                </div>

                <!-- Download Resume Button -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('resume.download') }}"
                        class="inline-flex items-center justify-center px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Download PDF Resume
                    </a>
                    <a href="{{ personal_contact('email_link') }}"
                        class="inline-flex items-center justify-center px-8 py-3 bg-white dark:bg-gray-800 text-gray-900 dark:text-white font-semibold rounded-lg border-2 border-primary-600 hover:bg-primary-50 dark:hover:bg-gray-700 transition-colors duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                            </path>
                        </svg>
                        Contact Me
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    {{-- <section class="py-8 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <form method="GET" class="flex flex-wrap gap-4 items-center justify-center">
                <!-- Section Filter -->
                <select name="section" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500">
                    <option value="all" {{ $section === 'all' ? 'selected' : '' }}>All Sections</option>
                    <option value="experience" {{ $section === 'experience' ? 'selected' : '' }}>Experience Only</option>
                    <option value="education" {{ $section === 'education' ? 'selected' : '' }}>Education Only</option>
                    <option value="certifications" {{ $section === 'certifications' ? 'selected' : '' }}>Certifications Only</option>
                </select>
                
                <!-- Technology Filter -->
                <select name="technology" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500">
                    <option value="">All Technologies</option>
                    @foreach ($technologies as $tech)
                        <option value="{{ $tech }}" {{ $technology === $tech ? 'selected' : '' }}>{{ $tech }}</option>
                    @endforeach
                </select>
                
                <!-- Year Filter -->
                <select name="year" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500">
                    <option value="">All Years</option>
                    @foreach ($years as $yr)
                        <option value="{{ $yr }}" {{ $year == $yr ? 'selected' : '' }}>{{ $yr }}</option>
                    @endforeach
                </select>
                
                <button type="submit" class="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors duration-200">
                    Filter
                </button>
                
                @if ($section !== 'all' || $technology || $year)
                    <a href="{{ url('/resume') }}" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200">
                        Clear
                    </a>
                @endif
            </form>
            
            <!-- Active Filters -->
            @if ($section !== 'all' || $technology || $year)
                <div class="mt-4 flex flex-wrap gap-2 justify-center">
                    @if ($section !== 'all')
                        <span class="filter-badge">Section: {{ ucfirst($section) }}</span>
                    @endif
                    @if ($technology)
                        <span class="filter-badge">Technology: {{ $technology }}</span>
                    @endif
                    @if ($year)
                        <span class="filter-badge">Year: {{ $year }}</span>
                    @endif
                </div>
            @endif
        </div>
    </div>
</section> --}}

    <!-- Mobile Navigation Toggle -->
    <button class="mobile-nav-toggle" onclick="toggleMobileSidebar()" aria-label="Toggle navigation menu">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    </button>

    <!-- Mobile Sidebar -->
    <div class="mobile-sidebar" id="mobileSidebar" onclick="closeMobileSidebar(event)">
        <div class="mobile-sidebar-content" onclick="event.stopPropagation()">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Resume Navigation</h3>
                    <button onclick="closeMobileSidebar()"
                        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                            </path>
                        </svg>
                    </button>
                </div>
                <nav id="mobileNavigation">
                    <!-- Navigation will be populated by JavaScript -->
                </nav>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <section class="py-16 bg-gray-50 dark:bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="max-w-7xl mx-auto">
                <div class="lg:grid lg:grid-cols-12 lg:gap-8">

                    <!-- Desktop Sidebar Navigation -->
                    <div class="desktop-sidebar lg:col-span-3">
                        <div class="sidebar-nav bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Resume Navigation</h3>
                            <nav id="desktopNavigation">
                                <!-- Navigation will be populated by JavaScript -->
                            </nav>
                        </div>
                    </div>

                    <!-- Main Content Area -->
                    <div class="lg:col-span-9">

                        <!-- Professional Experience Section -->
                        @if ($section === 'all' || $section === 'experience')
                            <div id="experience-section" class="resume-section section-card rounded-xl p-8 mb-12">
                                <div class="flex items-center mb-8">
                                    <div class="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8zM8 14v.01M12 14v.01M16 14v.01">
                                            </path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Professional Experience
                                        </h2>
                                        <p class="text-gray-600 dark:text-gray-300">{{ $totalExperience['years'] }}+ years
                                            of
                                            professional development experience</p>
                                    </div>
                                </div>

                                <div class="relative pl-8">
                                    @forelse($experiences as $index => $experience)
                                        <div class="timeline-item relative mb-12 last:mb-0">
                                            <div class="timeline-dot"></div>
                                            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg ml-8">
                                                <div
                                                    class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4">
                                                    <div class="flex-1">
                                                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                                            {{ $experience->title }}</h3>
                                                        <div
                                                            class="text-lg text-primary-600 dark:text-primary-400 font-semibold mb-2">
                                                            {{ $experience->company }}</div>
                                                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                                            {{ $experience->location }} •
                                                            {{ $experience->start_date->format('M Y') }} -
                                                            {{ $experience->end_date ? $experience->end_date->format('M Y') : 'Present' }}
                                                            @if ($experience->end_date)
                                                                ({{ $experience->start_date->diffInMonths($experience->end_date) }}
                                                                months)
                                                            @else
                                                                ({{ $experience->start_date->diffInMonths(now()) }} months)
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="prose dark:prose-invert max-w-none mb-4">
                                                    <p class="text-gray-700 dark:text-gray-300">
                                                        {{ $experience->description }}</p>
                                                </div>

                                                @if ($experience->technologies->count() > 0)
                                                    <div class="flex flex-wrap gap-2">
                                                        @foreach ($experience->technologies as $tech)
                                                            <span
                                                                class="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full text-sm font-medium">
                                                                {{ $tech->name }}
                                                            </span>
                                                        @endforeach
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @empty
                                        <div class="text-center py-8">
                                            <p class="text-gray-500 dark:text-gray-400">No experience records match your
                                                current
                                                filters.</p>
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        @endif

                        <!-- Education Section -->
                        @if ($section === 'all' || $section === 'education')
                            <div id="education-section" class="resume-section section-card rounded-xl p-8 mb-12">
                                <div class="flex items-center mb-8">
                                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                            </path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Education</h2>
                                        <p class="text-gray-600 dark:text-gray-300">Academic background and formal
                                            education</p>
                                    </div>
                                </div>

                                <div class="grid gap-6">
                                    @forelse($education as $edu)
                                        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                                            <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4">
                                                <div class="flex-1">
                                                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                                        {{ $edu->degree }}</h3>
                                                    <div
                                                        class="text-lg text-green-600 dark:text-green-400 font-semibold mb-2">
                                                        {{ $edu->institution }}</div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                                        {{ $edu->location }} •
                                                        {{ $edu->start_date->format('Y') }} -
                                                        {{ $edu->end_date ? $edu->end_date->format('Y') : 'Present' }}
                                                    </div>
                                                </div>
                                            </div>

                                            @if ($edu->description)
                                                <div class="prose dark:prose-invert max-w-none mb-4">
                                                    <p class="text-gray-700 dark:text-gray-300">{{ $edu->description }}
                                                    </p>
                                                </div>
                                            @endif

                                            @if ($edu->technologies->count() > 0)
                                                <div class="flex flex-wrap gap-2">
                                                    @foreach ($edu->technologies as $tech)
                                                        <span
                                                            class="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm font-medium">
                                                            {{ $tech->name }}
                                                        </span>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                    @empty
                                        <div class="text-center py-8">
                                            <p class="text-gray-500 dark:text-gray-400">No education records match your
                                                current
                                                filters.</p>
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        @endif

                        <!-- Certifications Section -->
                        @if ($section === 'all' || $section === 'certifications')
                            <div id="certifications-section" class="resume-section section-card rounded-xl p-8">
                                <div class="flex items-center mb-8">
                                    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z">
                                            </path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Certifications</h2>
                                        <p class="text-gray-600 dark:text-gray-300">Professional certifications and
                                            achievements
                                        </p>
                                    </div>
                                </div>

                                @if ($certificationsByProvider->count() > 0)
                                    @foreach ($certificationsByProvider as $provider => $certs)
                                        <div class="mb-8 last:mb-0">
                                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                                                {{ $provider }}</h3>
                                            <div class="grid gap-4">
                                                @foreach ($certs as $cert)
                                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                                                        <div
                                                            class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                                                            <div class="flex-1">
                                                                <h4
                                                                    class="text-lg font-bold text-gray-900 dark:text-white mb-2">
                                                                    {{ $cert->title }}</h4>
                                                                <div
                                                                    class="text-purple-600 dark:text-purple-400 font-semibold mb-2">
                                                                    {{ $cert->provider }}</div>
                                                                <div class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                                                    Issued:
                                                                    {{ \Carbon\Carbon::parse($cert->date)->format('M Y') }}
                                                                    @if ($cert->expiry_date)
                                                                        • Expires:
                                                                        {{ \Carbon\Carbon::parse($cert->expiry_date)->format('M Y') }}
                                                                    @endif
                                                                </div>

                                                                @if ($cert->description)
                                                                    <p class="text-gray-700 dark:text-gray-300 mb-4">
                                                                        {{ $cert->description }}</p>
                                                                @endif
                                                            </div>

                                                            @if ($cert->credential_url)
                                                                <div class="mt-4 lg:mt-0 lg:ml-4">
                                                                    <a href="{{ $cert->credential_url }}" target="_blank"
                                                                        rel="noopener noreferrer"
                                                                        class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200">
                                                                        <svg class="w-4 h-4 mr-2" fill="none"
                                                                            stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round"
                                                                                stroke-linejoin="round" stroke-width="2"
                                                                                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14">
                                                                            </path>
                                                                        </svg>
                                                                        View Credential
                                                                    </a>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="text-center py-8">
                                        <p class="text-gray-500 dark:text-gray-400">No certifications match your current
                                            filters.
                                        </p>
                                    </div>
                                @endif
                            </div>
                        @endif

                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-8 bg-primary-600 dark:bg-primary-800">
        <div class="container mx-auto px-4 text-center">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                    Ready to Work Together?
                </h2>
                <p class="text-xl text-primary-100 mb-8">
                    Let's discuss how my experience and skills can contribute to your next project.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ url('/contact-me') }}"
                        class="inline-flex items-center justify-center px-8 py-3 bg-white text-primary-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        Start a Conversation
                    </a>
                    <a href="{{ route('resume.download') }}"
                        class="inline-flex items-center justify-center px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-primary-600 transition-colors duration-200">
                        Download Resume
                    </a>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
    <script>
        // Navigation data structure
        const navigationData = [{
                id: 'experience-section',
                title: 'Professional Experience',
                icon: `<svg class="nav-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8zM8 14v.01M12 14v.01M16 14v.01"></path>
                </svg>`,
                subsections: []
            },
            {
                id: 'education-section',
                title: 'Education',
                icon: `<svg class="nav-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                </svg>`,
                subsections: []
            },
            {
                id: 'certifications-section',
                title: 'Certifications',
                icon: `<svg class="nav-section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                </svg>`,
                subsections: []
            }
        ];

        // Mobile sidebar functions
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('mobileSidebar');
            sidebar.classList.toggle('active');
        }

        function closeMobileSidebar(event) {
            if (event && event.target === event.currentTarget) {
                const sidebar = document.getElementById('mobileSidebar');
                sidebar.classList.remove('active');
            }
        }

        // Generate navigation HTML
        function generateNavigationHTML(data) {
            return data.map(section => {
                const subsectionHTML = section.subsections.length > 0 ?
                    `<div class="nav-subsection">
                        ${section.subsections.map(sub =>
                            `<a href="#${sub.id}" class="nav-link block py-2 px-3 text-sm text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                                    ${sub.title}
                                </a>`
                        ).join('')}
                    </div>` :
                    '';

                return `
                    <div class="nav-item mb-2" data-section="${section.id}">
                        <a href="#${section.id}" class="nav-link flex items-center py-3 px-4 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200 rounded-lg">
                            ${section.icon}
                            <span class="font-medium">${section.title}</span>
                        </a>
                        ${subsectionHTML}
                    </div>
                `;
            }).join('');
        }

        // Populate navigation menus
        function populateNavigation() {
            const desktopNav = document.getElementById('desktopNavigation');
            const mobileNav = document.getElementById('mobileNavigation');

            if (desktopNav) {
                desktopNav.innerHTML = generateNavigationHTML(navigationData);
            }

            if (mobileNav) {
                mobileNav.innerHTML = generateNavigationHTML(navigationData);
            }
        }

        // Smooth scroll to section
        function scrollToSection(targetId) {
            const target = document.getElementById(targetId);
            if (target) {
                const headerHeight = 80; // Account for fixed header
                const targetPosition = target.offsetTop - headerHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        }

        // Update active navigation item
        function updateActiveNavigation() {
            const sections = navigationData.map(item => item.id);
            let activeSection = '';

            // Find the currently visible section
            for (const sectionId of sections) {
                const element = document.getElementById(sectionId);
                if (element) {
                    const rect = element.getBoundingClientRect();
                    const headerHeight = 80;

                    if (rect.top <= headerHeight + 100 && rect.bottom >= headerHeight) {
                        activeSection = sectionId;
                        break;
                    }
                }
            }

            // Update active states
            document.querySelectorAll('.nav-item').forEach(item => {
                const sectionId = item.getAttribute('data-section');
                if (sectionId === activeSection) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Populate navigation menus
            populateNavigation();

            // Handle navigation clicks
            document.addEventListener('click', function(e) {
                const link = e.target.closest('a[href^="#"]');
                if (link) {
                    e.preventDefault();
                    const targetId = link.getAttribute('href').substring(1);
                    scrollToSection(targetId);

                    // Close mobile sidebar if open
                    const mobileSidebar = document.getElementById('mobileSidebar');
                    if (mobileSidebar.classList.contains('active')) {
                        mobileSidebar.classList.remove('active');
                    }
                }
            });

            // Update active navigation on scroll
            let scrollTimeout;
            window.addEventListener('scroll', function() {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(updateActiveNavigation, 50);
            });

            // Initial active navigation update
            updateActiveNavigation();

            // Add animation on scroll for timeline items
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.timeline-item, .section-card').forEach(el => {
                observer.observe(el);
            });

            // Handle escape key for mobile sidebar
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const mobileSidebar = document.getElementById('mobileSidebar');
                    if (mobileSidebar.classList.contains('active')) {
                        mobileSidebar.classList.remove('active');
                    }
                }
            });
        });
    </script>
@endpush
