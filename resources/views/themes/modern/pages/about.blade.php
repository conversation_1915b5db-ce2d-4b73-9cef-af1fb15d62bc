@extends(theme_view('layout'))

@section('title', $seoData['title'] ?? 'About <PERSON><PERSON><PERSON> - Senior Backend Developer')

@section('meta')
    @include(theme_view('partials.seo-meta'), ['seoData' => $seoData])
@endsection

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <!-- Breadcrumbs -->
        <div class="container mx-auto px-4 pt-8">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    @foreach ($breadcrumbs as $breadcrumb)
                        <li class="inline-flex items-center">
                            @if (!$loop->last && $breadcrumb['url'])
                                <a href="{{ $breadcrumb['url'] }}"
                                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    @if ($loop->first)
                                        <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                            fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                                        </svg>
                                    @endif
                                    {{ $breadcrumb['name'] }}
                                </a>
                            @else
                                <span
                                    class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{{ $breadcrumb['name'] }}</span>
                            @endif
                            @if (!$loop->last)
                                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 9 4-4-4-4" />
                                </svg>
                            @endif
                        </li>
                    @endforeach
                </ol>
            </nav>
        </div>

        <!-- Hero Section -->
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
                <div>
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                        About Zahir Hayrullah
                    </h1>
                    <p class="text-xl text-gray-600 dark:text-gray-300 mb-6">
                        {{ $aboutData['description'] ?? 'Passionate backend developer with over 7 years of experience in creating robust, scalable web applications. Based in Berlin, Germany, originally from Syria.' }}
                    </p>
                    <div class="flex flex-wrap gap-4">
                        <a href="{{ url('/resume/download') }}"
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                            Download Resume
                        </a>
                        <a href="{{ url('/contact-me') }}"
                            class="border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg hover:bg-blue-600 hover:text-white transition-colors duration-200">
                            Get In Touch
                        </a>
                    </div>
                </div>
                <div class="text-center">
                    <img src="{{ asset('images/zahir-profile.jpg') }}" alt="Zahir Hayrullah"
                        class="w-64 h-64 rounded-full mx-auto shadow-lg object-cover">
                </div>
            </div>

            <!-- Career Statistics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-center shadow-lg">
                    <div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                        {{ $careerStats['years_experience'] }}+</div>
                    <div class="text-gray-600 dark:text-gray-300">Years Experience</div>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-center shadow-lg">
                    <div class="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                        {{ $careerStats['projects_completed'] }}+</div>
                    <div class="text-gray-600 dark:text-gray-300">Projects Completed</div>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-center shadow-lg">
                    <div class="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                        {{ $careerStats['happy_clients'] }}+</div>
                    <div class="text-gray-600 dark:text-gray-300">Happy Clients</div>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-center shadow-lg">
                    <div class="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2">
                        {{ $careerStats['technologies_mastered'] }}+</div>
                    <div class="text-gray-600 dark:text-gray-300">Technologies</div>
                </div>
            </div>

            <!-- My Story -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-12">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">My Journey</h2>
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        Born and raised in Syria, I discovered my passion for programming during my Computer Engineering
                        studies at Sakarya University in Turkey.
                        What started as curiosity about how websites work evolved into a deep love for creating digital
                        solutions that make a real impact.
                    </p>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        After graduating, I began my professional journey as a Junior Web Developer, quickly advancing
                        through the ranks by consistently
                        delivering high-quality solutions and staying current with emerging technologies. My expertise in
                        PHP and Laravel has been
                        the foundation of my career, but I've continuously expanded my skill set to include modern frontend
                        frameworks and cloud technologies.
                    </p>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                        In 2013, I made Berlin my home, where I've had the opportunity to work with diverse teams and
                        contribute to innovative projects.
                        Currently, I'm a Senior Backend Developer at Content Fleet, where I lead development initiatives and
                        mentor junior developers.
                    </p>
                    <p class="text-gray-600 dark:text-gray-300">
                        When I'm not coding, I enjoy exploring Berlin's vibrant tech scene, contributing to open-source
                        projects, and sharing knowledge
                        with the developer community. I believe in the power of technology to bridge cultures and create
                        opportunities for everyone.
                    </p>
                </div>
            </div>

            <!-- Featured Experience -->
            @if ($featuredExperiences->count() > 0)
                <div class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Recent Experience</h2>
                    <div class="space-y-6">
                        @foreach ($featuredExperiences as $experience)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                    <div>
                                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                            {{ $experience->title }}</h3>
                                        <p class="text-blue-600 dark:text-blue-400 font-medium">{{ $experience->company }}
                                        </p>
                                        <p class="text-gray-600 dark:text-gray-300 text-sm">{{ $experience->location }}</p>
                                    </div>
                                    <div class="text-sm text-gray-600 dark:text-gray-300 mt-2 md:mt-0">
                                        {{ $experience->start_date->format('M Y') }} -
                                        {{ $experience->is_current ? 'Present' : $experience->end_date->format('M Y') }}
                                    </div>
                                </div>
                                <p class="text-gray-600 dark:text-gray-300 mb-4">{{ $experience->description }}</p>
                                @if ($experience->technologies->count() > 0)
                                    <div class="flex flex-wrap gap-2">
                                        @foreach ($experience->technologies->take(6) as $tech)
                                            <span
                                                class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">
                                                {{ $tech->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                    <div class="text-center mt-6">
                        <a href="{{ url('/experience') }}" class="text-blue-600 dark:text-blue-400 hover:underline">
                            View Full Experience →
                        </a>
                    </div>
                </div>
            @endif

            <!-- Featured Technologies -->
            @if ($featuredTechnologies->count() > 0)
                <div class="mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Core Technologies</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        @foreach ($featuredTechnologies as $tech)
                            <div
                                class="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                                @if ($tech->icon_path)
                                    <img src="{{ asset($tech->icon_path) }}" alt="{{ $tech->name }}"
                                        class="w-8 h-8 mx-auto mb-2">
                                @else
                                    <div
                                        class="w-8 h-8 mx-auto mb-2 bg-blue-100 dark:bg-blue-900 rounded flex items-center justify-center">
                                        <span
                                            class="text-blue-600 dark:text-blue-400 font-bold text-sm">{{ substr($tech->name, 0, 2) }}</span>
                                    </div>
                                @endif
                                <h3 class="font-medium text-gray-900 dark:text-white text-sm">{{ $tech->name }}</h3>
                                <p class="text-xs text-gray-600 dark:text-gray-400">{{ $tech->years_experience }}y</p>
                            </div>
                        @endforeach
                    </div>
                    <div class="text-center mt-6">
                        <a href="{{ url('/skills') }}" class="text-blue-600 dark:text-blue-400 hover:underline">
                            View All Skills →
                        </a>
                    </div>
                </div>
            @endif

            <!-- Education & Certifications -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                <!-- Latest Education -->
                @if ($latestEducation)
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Education</h2>
                        <div class="border-l-4 border-blue-600 pl-4">
                            <h3 class="font-semibold text-gray-900 dark:text-white">{{ $latestEducation->degree }}</h3>
                            <p class="text-blue-600 dark:text-blue-400">{{ $latestEducation->institution }}</p>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">{{ $latestEducation->location }}</p>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">
                                {{ $latestEducation->start_date->format('Y') }} -
                                {{ $latestEducation->end_date->format('Y') }}
                            </p>
                        </div>
                        <div class="mt-4">
                            <a href="{{ url('/education') }}"
                                class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                                View Full Education →
                            </a>
                        </div>
                    </div>
                @endif

                <!-- Recent Certifications -->
                @if ($recentCertifications->count() > 0)
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Recent Certifications</h2>
                        <div class="space-y-3">
                            @foreach ($recentCertifications->take(3) as $cert)
                                <div class="border-l-4 border-green-600 pl-4">
                                    <h3 class="font-medium text-gray-900 dark:text-white text-sm">{{ $cert->title }}</h3>
                                    <p class="text-green-600 dark:text-green-400 text-sm">{{ $cert->provider }}</p>
                                    <p class="text-gray-600 dark:text-gray-300 text-xs">{{ $cert->date }}</p>
                                </div>
                            @endforeach
                        </div>
                        <div class="mt-4">
                            <a href="{{ url('/education') }}"
                                class="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                                View All Certifications →
                            </a>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Call to Action -->
            <div class="text-center">
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
                    <h2 class="text-2xl font-bold mb-4">Let's Work Together</h2>
                    <p class="text-blue-100 mb-6">Ready to bring your ideas to life? I'd love to hear about your project.
                    </p>
                    <a href="{{ url('/contact-me') }}"
                        class="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors duration-200">
                        Start a Conversation
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if (isset($seoData['structured_data']))
        <script type="application/ld+json">
        {!! json_encode($seoData['structured_data'], JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
    </script>
    @endif
@endsection
