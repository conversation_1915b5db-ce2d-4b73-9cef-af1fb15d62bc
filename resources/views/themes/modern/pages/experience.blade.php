@extends(theme_view('layout'))

@section('title', $seoData['title'] ?? 'Professional Experience')

@section('meta')
    @include(theme_view('partials.seo-meta'), ['seoData' => $seoData])
@endsection

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <!-- Breadcrumbs -->
        <div class="container mx-auto px-4 pt-8">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    @foreach ($breadcrumbs as $breadcrumb)
                        <li class="inline-flex items-center">
                            @if (!$loop->last && $breadcrumb['url'])
                                <a href="{{ $breadcrumb['url'] }}"
                                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    @if ($loop->first)
                                        <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                            fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                                        </svg>
                                    @endif
                                    {{ $breadcrumb['name'] }}
                                </a>
                            @else
                                <span
                                    class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{{ $breadcrumb['name'] }}</span>
                            @endif
                            @if (!$loop->last)
                                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 9 4-4-4-4" />
                                </svg>
                            @endif
                        </li>
                    @endforeach
                </ol>
            </nav>
        </div>

        <!-- Page Header -->
        <div class="container mx-auto px-4 py-12">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                    Professional Experience
                </h1>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    {{ $totalExperience['years'] }} years and {{ $totalExperience['months'] }} months of professional
                    development experience across various industries and technologies.
                </p>
            </div>

            <!-- Filters -->
            <div class="mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Filter Experience</h2>
                    <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Company</label>
                            <select name="company"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">All Companies</option>
                                @foreach ($companies as $comp)
                                    <option value="{{ $comp }}" {{ $company === $comp ? 'selected' : '' }}>
                                        {{ $comp }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label
                                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Technology</label>
                            <select name="technology"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">All Technologies</option>
                                @foreach ($technologies as $tech)
                                    <option value="{{ $tech }}" {{ $technology === $tech ? 'selected' : '' }}>
                                        {{ $tech }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Year</label>
                            <select name="year"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="">All Years</option>
                                @foreach ($years as $yr)
                                    <option value="{{ $yr }}" {{ $year == $yr ? 'selected' : '' }}>
                                        {{ $yr }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="md:col-span-3">
                            <button type="submit"
                                class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                Apply Filters
                            </button>
                            <a href="{{ url('/experience') }}"
                                class="ml-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-2 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors duration-200">
                                Clear Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Experience Timeline -->
            <div class="space-y-8">
                @forelse($experiences as $experience)
                    <div
                        class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <div class="p-8">
                            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                                <div class="flex-1">
                                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                        {{ $experience->title }}</h3>
                                    <div
                                        class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-gray-600 dark:text-gray-300">
                                        <div class="flex items-center mb-2 sm:mb-0">
                                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                            <span
                                                class="font-semibold text-blue-600 dark:text-blue-400">{{ $experience->company }}</span>
                                        </div>
                                        <div class="flex items-center mb-2 sm:mb-0">
                                            <svg class="w-5 h-5 mr-2 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                            <span>{{ $experience->location }}</span>
                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                            <span>{{ $experience->start_date->format('M Y') }} -
                                                {{ $experience->is_current ? 'Present' : $experience->end_date->format('M Y') }}</span>
                                        </div>
                                    </div>
                                </div>
                                @if ($experience->is_current)
                                    <div class="mt-4 lg:mt-0">
                                        <span
                                            class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
                                            Current Position
                                        </span>
                                    </div>
                                @endif
                            </div>

                            <div class="mb-6">
                                <p class="text-gray-600 dark:text-gray-300 leading-relaxed">{{ $experience->description }}
                                </p>
                            </div>

                            @if ($experience->responsibilities && count($experience->responsibilities) > 0)
                                <div class="mb-6">
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Key
                                        Responsibilities</h4>
                                    <ul class="space-y-2">
                                        @foreach ($experience->responsibilities as $responsibility)
                                            <li class="flex items-start">
                                                <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0"
                                                    fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                                <span class="text-gray-600 dark:text-gray-300">{{ $responsibility }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if ($experience->achievements && count($experience->achievements) > 0)
                                <div class="mb-6">
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Key Achievements
                                    </h4>
                                    <ul class="space-y-2">
                                        @foreach ($experience->achievements as $achievement)
                                            <li class="flex items-start">
                                                <svg class="w-5 h-5 text-green-600 mr-2 mt-0.5 flex-shrink-0"
                                                    fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                                <span class="text-gray-600 dark:text-gray-300">{{ $achievement }}</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if ($experience->technologies->count() > 0)
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Technologies Used
                                    </h4>
                                    <div class="flex flex-wrap gap-2">
                                        @foreach ($experience->technologies as $tech)
                                            <span
                                                class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
                                                {{ $tech->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @empty
                    <div class="text-center py-12">
                        <div class="text-gray-400 dark:text-gray-600 mb-4">
                            <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No experience found</h3>
                        <p class="text-gray-600 dark:text-gray-400">Try adjusting your filters or check back later.</p>
                    </div>
                @endforelse
            </div>

            <!-- Call to Action -->
            <div class="text-center mt-12">
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
                    <h2 class="text-2xl font-bold mb-4">Interested in Working Together?</h2>
                    <p class="text-blue-100 mb-6">Let's discuss how my experience can contribute to your next project.</p>
                    <div class="space-x-4">
                        <a href="{{ url('/contact-me') }}"
                            class="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors duration-200">
                            Get In Touch
                        </a>
                        <a href="{{ url('/resume/download') }}"
                            class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200">
                            Download Resume
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (isset($seoData['structured_data']))
        <script type="application/ld+json">
        {!! json_encode($seoData['structured_data'], JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
    </script>
    @endif
@endsection
