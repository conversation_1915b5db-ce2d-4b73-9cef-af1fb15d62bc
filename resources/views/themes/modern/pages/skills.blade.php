@extends(theme_view('layout'))

@section('title', $seoData['title'] ?? 'Technical Skills & Technologies')

@section('meta')
    @include(theme_view('partials.seo-meta'), ['seoData' => $seoData])
@endsection

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
    <!-- Breadcrumbs -->
    <div class="container mx-auto px-4 pt-8">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                @foreach($breadcrumbs as $breadcrumb)
                    <li class="inline-flex items-center">
                        @if(!$loop->last && $breadcrumb['url'])
                            <a href="{{ $breadcrumb['url'] }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                @if($loop->first)
                                    <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                                    </svg>
                                @endif
                                {{ $breadcrumb['name'] }}
                            </a>
                        @else
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{{ $breadcrumb['name'] }}</span>
                        @endif
                        @if(!$loop->last)
                            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                        @endif
                    </li>
                @endforeach
            </ol>
        </nav>
    </div>

    <!-- Page Header -->
    <div class="container mx-auto px-4 py-12">
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                Technical Skills & Technologies
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Explore my technical expertise and proficiency levels across various programming languages, frameworks, and tools.
            </p>
        </div>

        <!-- Filters -->
        <div class="mb-8">
            <div class="flex flex-wrap justify-center gap-4">
                @foreach($categories as $cat => $label)
                    <a href="{{ url('/skills') }}?category={{ $cat }}" 
                       class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 
                              {{ $category === $cat ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-blue-50 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700' }}">
                        {{ $label }}
                    </a>
                @endforeach
            </div>
        </div>

        <!-- Featured Technologies -->
        @if($featuredTechnologies->count() > 0)
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">Featured Technologies</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                @foreach($featuredTechnologies as $tech)
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 text-center">
                        @if($tech->icon_path)
                            <img src="{{ asset($tech->icon_path) }}" alt="{{ $tech->name }}" class="w-12 h-12 mx-auto mb-3">
                        @else
                            <div class="w-12 h-12 mx-auto mb-3 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                <span class="text-blue-600 dark:text-blue-400 font-bold text-lg">{{ substr($tech->name, 0, 2) }}</span>
                            </div>
                        @endif
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-2">{{ $tech->name }}</h3>
                        <div class="flex justify-center mb-2">
                            @for($i = 1; $i <= 5; $i++)
                                <svg class="w-4 h-4 {{ $i <= $tech->proficiency_level ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600' }}" 
                                     fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                            @endfor
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $tech->years_experience }} years</p>
                    </div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- All Technologies by Category -->
        <div class="space-y-12">
            @foreach($technologies as $categoryName => $techs)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 capitalize">
                        {{ str_replace('_', ' ', $categoryName) }} Technologies
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($techs as $tech)
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow duration-300">
                                <div class="flex items-center mb-4">
                                    @if($tech->icon_path)
                                        <img src="{{ asset($tech->icon_path) }}" alt="{{ $tech->name }}" class="w-8 h-8 mr-3">
                                    @else
                                        <div class="w-8 h-8 mr-3 bg-blue-100 dark:bg-blue-900 rounded flex items-center justify-center">
                                            <span class="text-blue-600 dark:text-blue-400 font-bold text-sm">{{ substr($tech->name, 0, 2) }}</span>
                                        </div>
                                    @endif
                                    <h3 class="font-semibold text-gray-900 dark:text-white">{{ $tech->name }}</h3>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Proficiency</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $tech->proficiency_level }}/5</span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ ($tech->proficiency_level / 5) * 100 }}%"></div>
                                    </div>
                                </div>
                                
                                <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                                    <span>{{ $tech->years_experience }} years experience</span>
                                    @if($tech->is_featured)
                                        <span class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full text-xs">Featured</span>
                                    @endif
                                </div>
                                
                                @if($tech->description)
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-3">{{ $tech->description }}</p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-12">
            <div class="bg-blue-600 rounded-lg p-8 text-white">
                <h2 class="text-2xl font-bold mb-4">Ready to Work Together?</h2>
                <p class="text-blue-100 mb-6">Let's discuss how my technical skills can help bring your project to life.</p>
                <a href="{{ url('/contact-us') }}" class="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors duration-200">
                    Get In Touch
                </a>
            </div>
        </div>
    </div>
</div>

@if(isset($seoData['structured_data']))
    <script type="application/ld+json">
        {!! json_encode($seoData['structured_data'], JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
    </script>
@endif
@endsection
