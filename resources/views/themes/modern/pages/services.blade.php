@extends(theme_view('layout'))

@section('title', $seoData['title'] ?? 'Professional Web Development Services')

@section('meta')
    @include(theme_view('partials.seo-meta'), ['seoData' => $seoData])
@endsection

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <!-- Breadcrumbs -->
        <div class="container mx-auto px-4 pt-8">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    @foreach ($breadcrumbs as $breadcrumb)
                        <li class="inline-flex items-center">
                            @if (!$loop->last && $breadcrumb['url'])
                                <a href="{{ $breadcrumb['url'] }}"
                                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                                    @if ($loop->first)
                                        <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                            fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                                        </svg>
                                    @endif
                                    {{ $breadcrumb['name'] }}
                                </a>
                            @else
                                <span
                                    class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">{{ $breadcrumb['name'] }}</span>
                            @endif
                            @if (!$loop->last)
                                <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                        stroke-width="2" d="m1 9 4-4-4-4" />
                                </svg>
                            @endif
                        </li>
                    @endforeach
                </ol>
            </nav>
        </div>

        <!-- Page Header -->
        <div class="container mx-auto px-4 py-12">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                    Professional Web Development Services
                </h1>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                    Comprehensive web development solutions tailored to your business needs. From backend architecture to
                    full-stack applications.
                </p>
            </div>

            <!-- Service Categories Filter -->
            @if ($categories->count() > 1)
                <div class="mb-8">
                    <div class="flex flex-wrap justify-center gap-4">
                        @foreach ($categories as $cat => $label)
                            <a href="{{ url('/services') }}?category={{ $cat }}"
                                class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 
                              {{ $category === $cat ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-blue-50 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700' }}">
                                {{ $label }}
                            </a>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Services Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                @forelse($services as $service)
                    <div
                        class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        @if (isset($service['image']))
                            <img src="{{ asset($service['image']) }}" alt="{{ $service['title'] }}"
                                class="w-full h-48 object-cover">
                        @else
                            <div
                                class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                <div class="text-white text-center">
                                    <div class="text-4xl mb-2">{{ $service['icon'] ?? '🚀' }}</div>
                                    <h3 class="text-xl font-bold">{{ $service['title'] }}</h3>
                                </div>
                            </div>
                        @endif

                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">{{ $service['title'] }}</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-4">{{ $service['description'] }}</p>

                            @if (isset($service['features']) && is_array($service['features']))
                                <ul class="space-y-2 mb-4">
                                    @foreach (array_slice($service['features'], 0, 3) as $feature)
                                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor"
                                                viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                            {{ $feature }}
                                        </li>
                                    @endforeach
                                </ul>
                            @endif

                            <div class="flex justify-between items-center">
                                @if (isset($service['price']))
                                    <span
                                        class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ $service['price'] }}</span>
                                @endif

                                @if (isset($service['slug']))
                                    <a href="{{ url('/services/' . $service['slug']) }}"
                                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                        Learn More
                                    </a>
                                @else
                                    <a href="{{ url('/contact-me') }}"
                                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                        Get Quote
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 dark:text-gray-600 mb-4">
                            <svg class="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No services found</h3>
                        <p class="text-gray-600 dark:text-gray-400">Try adjusting your filters or check back later.</p>
                    </div>
                @endforelse
            </div>

            <!-- Why Choose Me Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-12">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">Why Choose My Services?</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div
                            class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Quality Assurance</h3>
                        <p class="text-gray-600 dark:text-gray-300">Rigorous testing and code review processes ensure
                            high-quality deliverables.</p>
                    </div>
                    <div class="text-center">
                        <div
                            class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">On-Time Delivery</h3>
                        <p class="text-gray-600 dark:text-gray-300">Committed to meeting deadlines and delivering projects
                            on schedule.</p>
                    </div>
                    <div class="text-center">
                        <div
                            class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="currentColor"
                                viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Ongoing Support</h3>
                        <p class="text-gray-600 dark:text-gray-300">Comprehensive support and maintenance to keep your
                            applications running smoothly.</p>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center">
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
                    <h2 class="text-2xl font-bold mb-4">Ready to Start Your Project?</h2>
                    <p class="text-blue-100 mb-6">Let's discuss your requirements and create something amazing together.</p>
                    <div class="space-x-4">
                        <a href="{{ url('/contact-me') }}"
                            class="inline-block bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors duration-200">
                            Get Free Quote
                        </a>
                        <a href="{{ url('/about-me') }}"
                            class="inline-block border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200">
                            Learn More About Me
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (isset($seoData['structured_data']))
        <script type="application/ld+json">
        {!! json_encode($seoData['structured_data'], JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
    </script>
    @endif
@endsection
