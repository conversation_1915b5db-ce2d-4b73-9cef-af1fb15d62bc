<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="{{ theme_is_dark_mode() ? 'dark' : '' }}"
    x-data="{ darkMode: {{ theme_is_dark_mode() ? 'true' : 'false' }} }" :class="{ 'dark': darkMode }">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- SEO Meta Tags -->
    @include('themes.modern.partials.seo-meta')

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ theme_asset('images/favicon.ico') }}">
    <link rel="apple-touch-icon" href="{{ theme_asset('images/apple-touch-icon.png') }}">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Critical CSS -->
    <style>
        {!! app(\App\Services\PageSpeedOptimizationService::class)->generateCriticalCss() !!}
    </style>

    <!-- Resource Hints for Performance -->
    @php
        $resourceHints = app(\App\Services\PageSpeedOptimizationService::class)->generateResourceHints();
    @endphp

    @foreach ($resourceHints['dns-prefetch'] as $domain)
        <link rel="dns-prefetch" href="{{ $domain }}">
    @endforeach

    @foreach ($resourceHints['preconnect'] as $domain)
        <link rel="preconnect" href="{{ $domain }}" crossorigin>
    @endforeach

    @foreach ($resourceHints['preload'] as $resource)
        <link rel="preload" href="{{ $resource['href'] }}" as="{{ $resource['as'] }}"
            @if (isset($resource['crossorigin'])) crossorigin="{{ $resource['crossorigin'] }}" @endif
            onload="this.onload=null;this.rel='stylesheet'">
    @endforeach

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/themes/modern/app.js'])
    @stack('styles')

    <!-- Fallback for non-JS users -->
    <noscript>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap"
            rel="stylesheet">
    </noscript>

    <!-- Theme Configuration -->
    <script>
        window.themeConfig = @json(theme_config_get());
        window.isDarkMode = {{ theme_is_dark_mode() ? 'true' : 'false' }};
    </script>

    @yield('head')
</head>

<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300"
    x-data="themeManager()" x-init="init()">

    <!-- Skip to main content for accessibility -->
    <a href="#main-content"
        class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <!-- Loading Screen -->
    <div id="loading-screen"
        class="fixed inset-0 bg-white dark:bg-gray-900 z-50 flex items-center justify-center transition-opacity duration-500"
        x-data="{ show: true }" x-show="show" x-transition:leave="transition ease-in duration-500"
        x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p class="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
    </div>

    <!-- Header -->
    @include('themes.modern.partials.header')

    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        @yield('content')
    </main>

    <!-- Footer -->
    @include('themes.modern.partials.footer')

    <!-- Back to Top Button -->
    <button id="back-to-top"
        class="fixed bottom-8 right-8 bg-primary-600 hover:bg-primary-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 transform translate-y-16 opacity-0 z-40"
        x-data="backToTop()" x-show="show" x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform translate-y-16"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        x-transition:leave="transition ease-in duration-300"
        x-transition:leave-start="opacity-100 transform translate-y-0"
        x-transition:leave-end="opacity-0 transform translate-y-16" @click="scrollToTop()" aria-label="Back to top">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- Theme Toggle (Mobile) -->
    <div class="fixed bottom-8 left-8 md:hidden z-40">
        @include('themes.modern.partials.theme-toggle')
    </div>

    <!-- Scripts -->
    @stack('scripts')

    <!-- Analytics -->
    @if (config('services.google_analytics.id'))
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google_analytics.id') }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', '{{ config('services.google_analytics.id') }}');
        </script>
    @endif

    @yield('scripts')
</body>

</html>
