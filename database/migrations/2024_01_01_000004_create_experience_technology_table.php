<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExperienceTechnologyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('experience_technology', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            $table->unsignedBigInteger('experience_id');
            $table->unsignedBigInteger('technology_id');
            
            // Additional pivot data
            $table->integer('proficiency_gained')->nullable(); // 1-5 scale
            $table->boolean('is_primary')->default(false); // Primary technology for this experience
            
            $table->timestamps();
            
            // Foreign keys
            $table->foreign('experience_id')->references('id')->on('experiences')->onDelete('cascade');
            $table->foreign('technology_id')->references('id')->on('technologies')->onDelete('cascade');
            
            // Unique constraint
            $table->unique(['experience_id', 'technology_id']);
            
            // Indexes
            $table->index('experience_id');
            $table->index('technology_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('experience_technology');
    }
}
