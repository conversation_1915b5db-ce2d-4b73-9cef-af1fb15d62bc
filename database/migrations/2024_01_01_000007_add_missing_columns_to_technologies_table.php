<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToTechnologiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('technologies', function (Blueprint $table) {
            if (!Schema::hasColumn('technologies', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('icon_path');
            }
            if (!Schema::hasColumn('technologies', 'is_featured')) {
                $table->boolean('is_featured')->default(false)->after('is_active');
            }
            if (!Schema::hasColumn('technologies', 'slug')) {
                $table->string('slug')->unique()->nullable()->after('name');
            }
            if (!Schema::hasColumn('technologies', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('is_featured');
            }
            if (!Schema::hasColumn('technologies', 'description')) {
                $table->text('description')->nullable()->after('name');
            }
            if (!Schema::hasColumn('technologies', 'show_on_resume')) {
                $table->boolean('show_on_resume')->default(true)->after('is_active');
            }

            // Indexes will be added separately if needed
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('technologies', function (Blueprint $table) {
            // Drop columns if they exist
            if (Schema::hasColumn('technologies', 'is_active')) {
                $table->dropColumn('is_active');
            }
            if (Schema::hasColumn('technologies', 'is_featured')) {
                $table->dropColumn('is_featured');
            }
            if (Schema::hasColumn('technologies', 'slug')) {
                $table->dropColumn('slug');
            }
            if (Schema::hasColumn('technologies', 'sort_order')) {
                $table->dropColumn('sort_order');
            }
            if (Schema::hasColumn('technologies', 'description')) {
                $table->dropColumn('description');
            }
            if (Schema::hasColumn('technologies', 'show_on_resume')) {
                $table->dropColumn('show_on_resume');
            }
        });
    }
}
