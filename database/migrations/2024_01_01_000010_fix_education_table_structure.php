<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class FixEducationTableStructure extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if education table exists and add missing columns
        if (Schema::hasTable('education')) {
            Schema::table('education', function (Blueprint $table) {
                // Add missing columns if they don't exist
                if (!Schema::hasColumn('education', 'title')) {
                    $table->string('title')->after('id');
                }
                if (!Schema::hasColumn('education', 'institution')) {
                    $table->string('institution')->after('title');
                }
                if (!Schema::hasColumn('education', 'location')) {
                    $table->string('location')->nullable()->after('institution');
                }
                if (!Schema::hasColumn('education', 'degree')) {
                    $table->string('degree')->nullable()->after('location');
                }
                if (!Schema::hasColumn('education', 'field')) {
                    $table->string('field')->nullable()->after('degree');
                }
                if (!Schema::hasColumn('education', 'start_date')) {
                    $table->date('start_date')->after('field');
                }
                if (!Schema::hasColumn('education', 'end_date')) {
                    $table->date('end_date')->nullable()->after('start_date');
                }
                if (!Schema::hasColumn('education', 'description')) {
                    $table->text('description')->nullable()->after('end_date');
                }
                if (!Schema::hasColumn('education', 'coursework')) {
                    $table->json('coursework')->nullable()->after('description');
                }
                if (!Schema::hasColumn('education', 'projects')) {
                    $table->json('projects')->nullable()->after('coursework');
                }
            });
        } else {
            // Create the education table if it doesn't exist
            Schema::create('education', function (Blueprint $table) {
                $table->bigIncrements('id');

                // Basic Information
                $table->string('title');
                $table->string('institution');
                $table->string('location')->nullable();
                $table->string('degree')->nullable();
                $table->string('field')->nullable();

                // Dates
                $table->date('start_date');
                $table->date('end_date')->nullable();

                // Content
                $table->text('description')->nullable();
                $table->json('coursework')->nullable(); // Array of courses
                $table->json('projects')->nullable(); // Array of projects

                // Display Options
                $table->integer('sort_order')->default(0);
                $table->boolean('is_active')->default(true);
                $table->boolean('show_on_resume')->default(true);

                $table->unsignedBigInteger('created_by')->nullable();
                $table->unsignedBigInteger('modified_by')->nullable();
                $table->unsignedBigInteger('deleted_by')->nullable();
                $table->softDeletes();
                $table->timestamps();

                // Indexes
                $table->index(['is_active', 'sort_order']);
                $table->index('start_date');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This migration is meant to fix structure, so we don't reverse it
        // If needed, the original education table migration can be rolled back
    }
}
