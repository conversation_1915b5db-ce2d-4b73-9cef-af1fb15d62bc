<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTechnologiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('technologies', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            // Basic Information
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('category'); // backend, frontend, database, tools, mobile, devops
            $table->string('subcategory')->nullable(); // frameworks, languages, databases, etc.
            
            // Proficiency & Experience
            $table->integer('proficiency_level')->default(1); // 1-5 scale
            $table->decimal('years_experience', 4, 1)->default(0); // Years of experience
            
            // Display Information
            $table->string('icon_path')->nullable();
            $table->string('color')->nullable(); // Hex color for display
            $table->text('description')->nullable();
            $table->string('official_website')->nullable();
            
            // Display Options
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('show_on_resume')->default(true);
            $table->boolean('is_featured')->default(false);

            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->unsignedBigInteger('deleted_by')->nullable();
            $table->softDeletes();
            $table->timestamps();
            
            // Indexes
            $table->index(['category', 'is_active', 'sort_order']);
            $table->index(['is_featured', 'proficiency_level']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('technologies');
    }
}
