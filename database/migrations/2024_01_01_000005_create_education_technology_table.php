<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEducationTechnologyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('education_technology', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            $table->unsignedBigInteger('education_id');
            $table->unsignedBigInteger('technology_id');
            
            // Additional pivot data
            $table->string('context')->nullable(); // coursework, project, thesis, etc.
            $table->boolean('is_primary')->default(false);
            
            $table->timestamps();
            
            // Foreign keys
            $table->foreign('education_id')->references('id')->on('education')->onDelete('cascade');
            $table->foreign('technology_id')->references('id')->on('technologies')->onDelete('cascade');
            
            // Unique constraint
            $table->unique(['education_id', 'technology_id']);
            
            // Indexes
            $table->index('education_id');
            $table->index('technology_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('education_technology');
    }
}
