<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEducationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('education', function (Blueprint $table) {
            $table->bigIncrements('id');

            // Basic Information
            $table->string('title');
            $table->string('institution');
            $table->string('location')->nullable();
            $table->string('degree')->nullable();
            $table->string('field')->nullable();

            // Dates
            $table->date('start_date');
            $table->date('end_date')->nullable();

            // Content
            $table->text('description')->nullable();
            $table->json('coursework')->nullable(); // Array of courses
            $table->json('projects')->nullable(); // Array of projects

            // Display Options
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('show_on_resume')->default(true);

            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->unsignedBigInteger('deleted_by')->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Indexes
            $table->index(['is_active', 'sort_order']);
            $table->index('start_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('education');
    }
}
