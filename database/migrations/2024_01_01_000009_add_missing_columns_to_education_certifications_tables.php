<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToEducationCertificationsTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add missing columns to education table
        Schema::table('education', function (Blueprint $table) {
            if (!Schema::hasColumn('education', 'is_active')) {
                $table->boolean('is_active')->default(true);
            }
            if (!Schema::hasColumn('education', 'show_on_resume')) {
                $table->boolean('show_on_resume')->default(true);
            }
            if (!Schema::hasColumn('education', 'sort_order')) {
                $table->integer('sort_order')->default(0);
            }
        });

        // Add missing columns to certifications table
        Schema::table('certifications', function (Blueprint $table) {
            if (!Schema::hasColumn('certifications', 'is_active')) {
                $table->boolean('is_active')->default(true);
            }
            if (!Schema::hasColumn('certifications', 'sort_order')) {
                $table->integer('sort_order')->default(0);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove columns from education table
        Schema::table('education', function (Blueprint $table) {
            if (Schema::hasColumn('education', 'is_active')) {
                $table->dropColumn('is_active');
            }
            if (Schema::hasColumn('education', 'show_on_resume')) {
                $table->dropColumn('show_on_resume');
            }
            if (Schema::hasColumn('education', 'sort_order')) {
                $table->dropColumn('sort_order');
            }
        });

        // Remove columns from certifications table
        Schema::table('certifications', function (Blueprint $table) {
            if (Schema::hasColumn('certifications', 'is_active')) {
                $table->dropColumn('is_active');
            }
            if (Schema::hasColumn('certifications', 'sort_order')) {
                $table->dropColumn('sort_order');
            }
        });
    }
}
