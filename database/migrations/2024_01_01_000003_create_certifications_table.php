<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCertificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('certifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            // Basic Information
            $table->string('title');
            $table->string('provider');
            $table->string('credential_id')->nullable();
            $table->date('issue_date');
            $table->date('expiry_date')->nullable();
            
            // Content
            $table->text('description')->nullable();
            $table->string('credential_url')->nullable();
            $table->string('verification_url')->nullable();
            $table->string('certificate_image')->nullable();
            
            // Skills & Technologies
            $table->json('skills_gained')->nullable(); // Array of skills
            
            // Display Options
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('show_on_resume')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->boolean('never_expires')->default(false);

            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->unsignedBigInteger('deleted_by')->nullable();
            $table->softDeletes();
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active', 'sort_order']);
            $table->index(['provider', 'issue_date']);
            $table->index('issue_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('certifications');
    }
}
