<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToExperiencesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('experiences', function (Blueprint $table) {
            if (!Schema::hasColumn('experiences', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('is_featured');
            }
            if (!Schema::hasColumn('experiences', 'show_on_resume')) {
                $table->boolean('show_on_resume')->default(true)->after('is_active');
            }
            
            // Add indexes
            $table->index('is_active');
            $table->index('show_on_resume');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('experiences', function (Blueprint $table) {
            $table->dropIndex(['is_active']);
            $table->dropIndex(['show_on_resume']);
            
            $table->dropColumn([
                'is_active',
                'show_on_resume'
            ]);
        });
    }
}
