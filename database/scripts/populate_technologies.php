<?php

require_once __DIR__ . '/../../vendor/autoload.php';

use App\Models\Technology;

// Technology data
$technologies = [
    // Backend Technologies
    ['name' => 'PHP', 'category' => 'backend', 'proficiency_level' => 5, 'years_experience' => 7.0, 'is_featured' => true],
    ['name' => 'Laravel', 'category' => 'backend', 'proficiency_level' => 5, 'years_experience' => 6.0, 'is_featured' => true],
    ['name' => 'Symfony', 'category' => 'backend', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => false],
    ['name' => 'CodeIgniter', 'category' => 'backend', 'proficiency_level' => 4, 'years_experience' => 2.0, 'is_featured' => false],
    ['name' => 'Node.js', 'category' => 'backend', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
    
    // Frontend Technologies
    ['name' => 'JavaScript', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 5.0, 'is_featured' => true],
    ['name' => 'Vue.js', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => true],
    ['name' => 'React', 'category' => 'frontend', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
    ['name' => 'jQuery', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 4.0, 'is_featured' => false],
    ['name' => 'HTML5', 'category' => 'frontend', 'proficiency_level' => 5, 'years_experience' => 7.0, 'is_featured' => true],
    ['name' => 'CSS3', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 6.0, 'is_featured' => true],
    ['name' => 'Bootstrap', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 5.0, 'is_featured' => false],
    ['name' => 'Tailwind CSS', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 2.0, 'is_featured' => true],
    
    // Database Technologies
    ['name' => 'MySQL', 'category' => 'database', 'proficiency_level' => 5, 'years_experience' => 7.0, 'is_featured' => true],
    ['name' => 'PostgreSQL', 'category' => 'database', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => true],
    ['name' => 'MongoDB', 'category' => 'database', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
    ['name' => 'Redis', 'category' => 'database', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => false],
    ['name' => 'SQLite', 'category' => 'database', 'proficiency_level' => 4, 'years_experience' => 4.0, 'is_featured' => false],
    
    // Tools & DevOps
    ['name' => 'Git', 'category' => 'tools', 'proficiency_level' => 5, 'years_experience' => 7.0, 'is_featured' => true],
    ['name' => 'Docker', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => true],
    ['name' => 'Linux', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 5.0, 'is_featured' => false],
    ['name' => 'Apache', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 5.0, 'is_featured' => false],
    ['name' => 'Nginx', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 4.0, 'is_featured' => false],
    ['name' => 'Composer', 'category' => 'tools', 'proficiency_level' => 5, 'years_experience' => 6.0, 'is_featured' => false],
    ['name' => 'NPM', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 4.0, 'is_featured' => false],
    ['name' => 'Webpack', 'category' => 'tools', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
    ['name' => 'Vite', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 2.0, 'is_featured' => false],
    
    // Cloud & Services
    ['name' => 'AWS', 'category' => 'cloud', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
    ['name' => 'DigitalOcean', 'category' => 'cloud', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => false],
    ['name' => 'Cloudflare', 'category' => 'cloud', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => false],
];

echo "Starting technology data population...\n";

foreach ($technologies as $techData) {
    try {
        $existing = Technology::where('name', $techData['name'])->first();
        
        if (!$existing) {
            $techData['slug'] = \Illuminate\Support\Str::slug($techData['name']);
            $techData['is_active'] = true;
            $techData['sort_order'] = 0;
            
            Technology::create($techData);
            echo "Created: {$techData['name']}\n";
        } else {
            echo "Exists: {$techData['name']}\n";
        }
    } catch (Exception $e) {
        echo "Error creating {$techData['name']}: " . $e->getMessage() . "\n";
    }
}

echo "Technology data population completed!\n";
echo "Total technologies: " . Technology::count() . "\n";
