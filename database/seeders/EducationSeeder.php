<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Education;
use App\Models\Technology;

class EducationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get theme config data
        $educationData = theme_config_get('education.items', []);
        
        foreach ($educationData as $index => $edu) {
            $education = Education::create([
                'title' => $edu['title'],
                'institution' => $edu['institution'],
                'location' => $edu['location'] ?? null,
                'degree' => $edu['degree'] ?? null,
                'field' => $edu['field'] ?? null,
                'start_date' => $edu['start_date'],
                'end_date' => $edu['end_date'] ?? null,
                'description' => $edu['description'] ?? null,
                'coursework' => $edu['coursework'] ?? [],
                'projects' => $edu['projects'] ?? [],
                'sort_order' => $index + 1,
                'is_active' => true,
                'show_on_resume' => true,
            ]);

            // Attach technologies based on education type
            if ($edu['field'] === 'Computer Engineering') {
                $techNames = ['C#', '.NET', 'JavaScript', 'HTML5', 'CSS3', 'SQL Server', 'Git'];
                foreach ($techNames as $techName) {
                    $technology = Technology::where('name', $techName)->first();
                    if ($technology) {
                        $education->technologies()->attach($technology->id, [
                            'context' => 'coursework',
                            'is_primary' => in_array($techName, ['C#', '.NET', 'JavaScript']),
                        ]);
                    }
                }
            }
        }
    }
}
