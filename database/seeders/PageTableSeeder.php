<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PageTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('pages')->truncate();
        $this->newItem(
            'home',
            'Web Developer - Computer Engineer',
            'home',
            'Web Developer - Computer Engineer',
            'Who is <PERSON><PERSON><PERSON>? He is a web developer and graduated from the Sakarya University since 2019. He works for Imtilak Real Estate Company as a web developer.',
            'zaher,zahir ,<PERSON><PERSON><PERSON>,زاهر, زاهر خيرالله , مهندس حاسوب,Computer Engineer, backend web, web developer, developer, laravel developer,php, javascript, html ,css, programing languages, UI/UX, blog, posts, books, videos, tutorials'
        );
        $this->newItem('privacy-policy', 'privacy policy', 'privacy policy', 'privacy policy', 'privacy policy', 'create a website,web application development,ecommerce website design,website developers');
        $this->newItem('contact-me', 'Get In Touch', 'Contact Me', 'Contact Me', 'Willing to serve you and answer all your inquiries', 'create a website,web application development,ecommerce website design,website developers');
        $this->newItem('about-me', 'Who is Zahir Hayrullah', 'About Me', 'Who is Zahir Hayrullah', 'About Zahir Hayrullah,He is a web developer and was born in syria in 1996. He works in Imtilak Real Estate Company as a web developer.', 'create a website,web application development,ecommerce website design,website developers');
        $this->newItem('skills', 'Skills & Services Offers', 'Skills & Services Offers', 'Skills & Services Offers', 'skills', 'create a website,web application development,ecommerce website design,website developers');
        $this->newItem('projects', 'projects', 'projects', 'projects', 'projects', 'create a website,web application development,ecommerce website design,website developers');
        $this->newItem('services', 'services', 'services', 'services', 'services', 'create a website,web application development,ecommerce website design,website developers');
        $this->newItem('blog', 'blog', 'blog', 'blog', 'blog', 'create a website,web application development,ecommerce website design,website developers');
    }

    private function newItem($slug, $title, $content, $seo_title, $seo_description, $seo_keywords)
    {
        $page = new \App\Models\Page();
        $page->slug = Str::slug(Str::lower($slug), '-');
        $page->title = $title;
        $page->content = $content;
        $page->seo_title = $seo_title;
        $page->seo_description = $seo_description;
        $page->seo_keywords = $seo_keywords;
        $page->save();
    }
}
