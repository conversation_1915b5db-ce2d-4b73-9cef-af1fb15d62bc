<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Technology;

class TechnologySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $technologies = [
            // Backend Technologies
            [
                'name' => 'PHP',
                'category' => 'backend',
                'subcategory' => 'language',
                'proficiency_level' => 5,
                'years_experience' => 6.0,
                'color' => '#777BB4',
                'description' => 'Server-side scripting language for web development',
                'official_website' => 'https://www.php.net',
                'sort_order' => 1,
                'is_featured' => true,
            ],
            [
                'name' => 'Laravel',
                'category' => 'backend',
                'subcategory' => 'framework',
                'proficiency_level' => 5,
                'years_experience' => 5.0,
                'color' => '#FF2D20',
                'description' => 'PHP web application framework with elegant syntax',
                'official_website' => 'https://laravel.com',
                'sort_order' => 2,
                'is_featured' => true,
            ],
            [
                'name' => 'C#',
                'category' => 'backend',
                'subcategory' => 'language',
                'proficiency_level' => 4,
                'years_experience' => 3.0,
                'color' => '#239120',
                'description' => 'Modern, object-oriented programming language',
                'official_website' => 'https://docs.microsoft.com/en-us/dotnet/csharp/',
                'sort_order' => 3,
            ],
            [
                'name' => '.NET',
                'category' => 'backend',
                'subcategory' => 'framework',
                'proficiency_level' => 4,
                'years_experience' => 3.0,
                'color' => '#512BD4',
                'description' => 'Free, cross-platform, open-source developer platform',
                'official_website' => 'https://dotnet.microsoft.com',
                'sort_order' => 4,
            ],

            // Frontend Technologies
            [
                'name' => 'JavaScript',
                'category' => 'frontend',
                'subcategory' => 'language',
                'proficiency_level' => 4,
                'years_experience' => 5.0,
                'color' => '#F7DF1E',
                'description' => 'Dynamic programming language for web development',
                'official_website' => 'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
                'sort_order' => 1,
                'is_featured' => true,
            ],
            [
                'name' => 'Vue.js',
                'category' => 'frontend',
                'subcategory' => 'framework',
                'proficiency_level' => 4,
                'years_experience' => 3.0,
                'color' => '#4FC08D',
                'description' => 'Progressive JavaScript framework for building user interfaces',
                'official_website' => 'https://vuejs.org',
                'sort_order' => 2,
                'is_featured' => true,
            ],
            [
                'name' => 'jQuery',
                'category' => 'frontend',
                'subcategory' => 'library',
                'proficiency_level' => 4,
                'years_experience' => 4.0,
                'color' => '#0769AD',
                'description' => 'Fast, small, and feature-rich JavaScript library',
                'official_website' => 'https://jquery.com',
                'sort_order' => 3,
            ],
            [
                'name' => 'HTML5',
                'category' => 'frontend',
                'subcategory' => 'markup',
                'proficiency_level' => 5,
                'years_experience' => 6.0,
                'color' => '#E34F26',
                'description' => 'Standard markup language for creating web pages',
                'official_website' => 'https://developer.mozilla.org/en-US/docs/Web/HTML',
                'sort_order' => 4,
            ],
            [
                'name' => 'CSS3',
                'category' => 'frontend',
                'subcategory' => 'styling',
                'proficiency_level' => 4,
                'years_experience' => 6.0,
                'color' => '#1572B6',
                'description' => 'Style sheet language for describing presentation of documents',
                'official_website' => 'https://developer.mozilla.org/en-US/docs/Web/CSS',
                'sort_order' => 5,
            ],

            // Database Technologies
            [
                'name' => 'MySQL',
                'category' => 'database',
                'subcategory' => 'relational',
                'proficiency_level' => 5,
                'years_experience' => 6.0,
                'color' => '#4479A1',
                'description' => 'Open-source relational database management system',
                'official_website' => 'https://www.mysql.com',
                'sort_order' => 1,
                'is_featured' => true,
            ],
            [
                'name' => 'PostgreSQL',
                'category' => 'database',
                'subcategory' => 'relational',
                'proficiency_level' => 4,
                'years_experience' => 2.0,
                'color' => '#336791',
                'description' => 'Advanced open-source relational database',
                'official_website' => 'https://www.postgresql.org',
                'sort_order' => 2,
                'is_featured' => true,
            ],
            [
                'name' => 'SQL Server',
                'category' => 'database',
                'subcategory' => 'relational',
                'proficiency_level' => 3,
                'years_experience' => 2.0,
                'color' => '#CC2927',
                'description' => 'Microsoft relational database management system',
                'official_website' => 'https://www.microsoft.com/en-us/sql-server',
                'sort_order' => 3,
            ],
            [
                'name' => 'SQLite',
                'category' => 'database',
                'subcategory' => 'relational',
                'proficiency_level' => 4,
                'years_experience' => 3.0,
                'color' => '#003B57',
                'description' => 'Self-contained, serverless SQL database engine',
                'official_website' => 'https://www.sqlite.org',
                'sort_order' => 4,
            ],

            // Tools & DevOps
            [
                'name' => 'Git',
                'category' => 'tools',
                'subcategory' => 'version_control',
                'proficiency_level' => 5,
                'years_experience' => 6.0,
                'color' => '#F05032',
                'description' => 'Distributed version control system',
                'official_website' => 'https://git-scm.com',
                'sort_order' => 1,
                'is_featured' => true,
            ],
            [
                'name' => 'GitHub',
                'category' => 'tools',
                'subcategory' => 'platform',
                'proficiency_level' => 5,
                'years_experience' => 6.0,
                'color' => '#181717',
                'description' => 'Web-based Git repository hosting service',
                'official_website' => 'https://github.com',
                'sort_order' => 2,
            ],
            [
                'name' => 'GitLab',
                'category' => 'tools',
                'subcategory' => 'platform',
                'proficiency_level' => 4,
                'years_experience' => 3.0,
                'color' => '#FC6D26',
                'description' => 'Web-based DevOps lifecycle tool',
                'official_website' => 'https://gitlab.com',
                'sort_order' => 3,
            ],
            [
                'name' => 'Bitbucket',
                'category' => 'tools',
                'subcategory' => 'platform',
                'proficiency_level' => 3,
                'years_experience' => 2.0,
                'color' => '#0052CC',
                'description' => 'Git repository management solution',
                'official_website' => 'https://bitbucket.org',
                'sort_order' => 4,
            ],
            [
                'name' => 'PhpStorm',
                'category' => 'tools',
                'subcategory' => 'ide',
                'proficiency_level' => 5,
                'years_experience' => 5.0,
                'color' => '#000000',
                'description' => 'PHP IDE with smart code assistance',
                'official_website' => 'https://www.jetbrains.com/phpstorm/',
                'sort_order' => 5,
            ],
            [
                'name' => 'Docker',
                'category' => 'tools',
                'subcategory' => 'containerization',
                'proficiency_level' => 3,
                'years_experience' => 2.0,
                'color' => '#2496ED',
                'description' => 'Platform for developing, shipping, and running applications',
                'official_website' => 'https://www.docker.com',
                'sort_order' => 6,
            ],
            [
                'name' => 'Redis',
                'category' => 'database',
                'subcategory' => 'cache',
                'proficiency_level' => 3,
                'years_experience' => 2.0,
                'color' => '#DC382D',
                'description' => 'In-memory data structure store',
                'official_website' => 'https://redis.io',
                'sort_order' => 5,
            ],
            [
                'name' => 'AWS',
                'category' => 'tools',
                'subcategory' => 'cloud',
                'proficiency_level' => 3,
                'years_experience' => 1.5,
                'color' => '#FF9900',
                'description' => 'Amazon Web Services cloud platform',
                'official_website' => 'https://aws.amazon.com',
                'sort_order' => 7,
            ],
        ];

        foreach ($technologies as $tech) {
            Technology::create($tech);
        }
    }
}
