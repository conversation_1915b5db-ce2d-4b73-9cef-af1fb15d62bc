<?php

use Database\Seeders\UserSeeder;
use Database\Seeders\TechnologySeeder;
use Database\Seeders\ExperienceSeeder;
use Database\Seeders\EducationSeeder;
use Database\Seeders\CertificationSeeder;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call(RolesTableSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(SettingTableSeeder::class);
        $this->call(ColorTableSeeder::class);
        $this->call(SkillTableSeeder::class);
        $this->call(PageTableSeeder::class);

        // CV Data Seeders
        $this->call(TechnologySeeder::class);
        $this->call(ExperienceSeeder::class);
        $this->call(EducationSeeder::class);
        $this->call(CertificationSeeder::class);
    }
}
