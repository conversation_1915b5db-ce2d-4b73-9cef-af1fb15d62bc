<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Experience;
use App\Models\Technology;

class ExperienceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get theme config data
        $experienceData = theme_config_get('experience.items', []);
        
        foreach ($experienceData as $index => $exp) {
            $experience = Experience::create([
                'title' => $exp['title'],
                'company' => $exp['company'],
                'location' => $exp['location'] ?? null,
                'employment_type' => $exp['employment_type'] ?? 'full-time',
                'start_date' => $exp['start_date'],
                'end_date' => $exp['end_date'] ?? null,
                'is_current' => $exp['current'] ?? false,
                'description' => $exp['description'] ?? null,
                'responsibilities' => $exp['responsibilities'] ?? [],
                'achievements' => $exp['achievements'] ?? [],
                'sort_order' => $index + 1,
                'is_active' => true,
                'show_on_resume' => true,
                'is_featured' => $exp['featured'] ?? false,
            ]);

            // Attach technologies if they exist
            if (!empty($exp['technologies'])) {
                foreach ($exp['technologies'] as $techName) {
                    $technology = Technology::where('name', $techName)->first();
                    if ($technology) {
                        $experience->technologies()->attach($technology->id, [
                            'is_primary' => in_array($techName, ['PHP', 'Laravel', 'JavaScript', 'MySQL']),
                            'proficiency_gained' => $this->calculateProficiencyGained($techName),
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Calculate proficiency gained for a technology
     */
    private function calculateProficiencyGained(string $techName): int
    {
        $proficiencyMap = [
            'PHP' => 5,
            'Laravel' => 5,
            'MySQL' => 5,
            'JavaScript' => 4,
            'Vue.js' => 4,
            'Git' => 5,
            'Docker' => 3,
            'AWS' => 3,
            'Redis' => 3,
            'PostgreSQL' => 4,
        ];

        return $proficiencyMap[$techName] ?? 3;
    }
}
