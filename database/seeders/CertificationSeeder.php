<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Certification;

class CertificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get theme config data
        $certificationData = theme_config_get('education.certifications', []);
        
        foreach ($certificationData as $index => $cert) {
            Certification::create([
                'title' => $cert['title'],
                'provider' => $cert['provider'],
                'credential_id' => $cert['credential_id'] ?? null,
                'issue_date' => $cert['date'] . '-01-01', // Convert year to date
                'expiry_date' => null,
                'description' => $cert['description'] ?? null,
                'credential_url' => $cert['url'] ?? null,
                'verification_url' => null,
                'certificate_image' => null,
                'skills_gained' => $this->getSkillsForCertification($cert['title']),
                'sort_order' => $index + 1,
                'is_active' => true,
                'show_on_resume' => true,
                'is_featured' => $this->isFeaturedCertification($cert['title']),
                'never_expires' => true,
            ]);
        }
    }

    /**
     * Get skills gained from certification
     */
    private function getSkillsForCertification(string $title): array
    {
        $skillsMap = [
            'PHP & MySQL Web Development' => ['PHP', 'MySQL', 'Web Development', 'Database Design'],
            'React.js Fundamentals' => ['React.js', 'JavaScript', 'Frontend Development', 'Component Architecture'],
            'Ubuntu Server Essentials' => ['Linux', 'Ubuntu', 'Server Administration', 'Command Line'],
            'Build RESTful Serverless API' => ['API Development', 'REST', 'Serverless', 'Cloud Computing'],
            'Creating Calculator App Flutter' => ['Flutter', 'Dart', 'Mobile Development', 'Cross-platform'],
        ];

        foreach ($skillsMap as $certTitle => $skills) {
            if (str_contains($title, $certTitle) || str_contains($certTitle, $title)) {
                return $skills;
            }
        }

        return [];
    }

    /**
     * Check if certification should be featured
     */
    private function isFeaturedCertification(string $title): bool
    {
        $featuredCerts = [
            'PHP & MySQL Web Development',
            'React.js Fundamentals',
            'Build RESTful Serverless API',
        ];

        foreach ($featuredCerts as $featuredTitle) {
            if (str_contains($title, $featuredTitle) || str_contains($featuredTitle, $title)) {
                return true;
            }
        }

        return false;
    }
}
