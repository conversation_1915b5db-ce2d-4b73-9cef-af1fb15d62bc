<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Modern Theme Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for the modern theme.
    | You can customize various aspects of the theme here.
    |
    */

    'name' => 'modern',
    'title' => 'Zahir <PERSON>llah Theme',
    'description' => 'A modern, responsive portfolio theme built with Tailwind CSS and Alpine.js',
    'version' => '1.0.0',
    'author' => 'Your Name',

    /*
    |--------------------------------------------------------------------------
    | Theme Features
    |--------------------------------------------------------------------------
    */
    'features' => [
        'responsive' => true,
        'dark_mode' => true,
        'accessibility' => true,
        'lazy_loading' => true,
        'animations' => true,
        'seo_optimized' => true,
        'performance_optimized' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Hero Section Configuration
    |--------------------------------------------------------------------------
    */
    'hero' => [
        'name' => 'Zahir Hayrullah',
        'title' => 'Web Developer',
        'subtitle' => 'Web Developer',
        'greeting' => 'Hello!',
        'location' => 'based in Berlin, Germany',
        'description' => "I'm Zahir, a passionate backend developer who transforms complex problems into elegant solutions. Originally from Syria, I've been crafting digital experiences in Berlin since 2022. For me, coding isn't just a profession—it's an art form where logic meets creativity.",
        'image' => 'images/hero-avatar.jpg',
        'background' => 'images/hero-bg.jpg',
        'show_social_links' => true,
        'show_scroll_indicator' => true,
        'animation_delay' => 500,
        'cta_primary' => [
            'text' => 'Hire me',
            'url' => '#contact-section',
        ],
        'cta_secondary' => [
            'text' => 'My works',
            'url' => '#services-section',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | About Section Configuration
    |--------------------------------------------------------------------------
    */
    'about' => [
        'name' => 'Zahir Hayrullah',
        'birthday' => 'January 01, 1996',
        'profession' => 'Web Developer',
        'title' => 'About Me',
        'subtitle' => 'Crafting Digital Solutions with Passion',
        'bio' => "I'm Zahir, a <strong>senior backend developer</strong> with over 7 years of experience building scalable web applications. Originally from Syria, I've been calling Berlin home since 2022, where I've grown from a curious developer into a seasoned architect of digital solutions.<br><br>My journey in tech is driven by an insatiable curiosity and the belief that elegant code can solve real-world problems. I specialize in creating robust APIs, designing efficient databases, and building systems that scale. Whether it's architecting a complex CRM system or optimizing database performance, I approach each challenge with creativity and precision.<br><br>When I'm not coding, you'll find me exploring the latest tech trends, contributing to open-source projects, or enjoying a perfect cup of coffee while sketching out solutions to tomorrow's challenges. For me, programming isn't just work—it's a continuous journey of learning, creating, and pushing the boundaries of what's possible.",
        'description' => "A passionate backend developer who transforms complex business requirements into elegant, scalable solutions. With expertise in modern web technologies and a keen eye for system architecture, I help businesses build robust digital foundations.",
        'image' => 'images/about-image.jpg',
        'work_experience' => [
            'Currently leading backend development at <a href="https://contentfleet.com" rel="nofollow" target="_blank">Content Fleet</a> in Berlin, where I architect high-performance content management APIs and implement advanced workflow automation systems. My role involves technical leadership, mentoring developers, and driving innovation in content technology solutions.',
            'Previously served as CRM Developer at <a href="https://www.imtilakgroup.com" rel="nofollow" target="_blank">IMTILAK Group</a> in Istanbul, a diversified conglomerate spanning twelve companies across real estate, tourism, healthcare, education, and e-commerce sectors. Led the development of multi-tenant CRM systems and integrated business solutions.',
            'Gained foundational experience as Web Developer at Imtilak Real Estate, focusing on property management systems and customer portals, and started my career as a Developer at Binoplus, where I learned industry best practices and full-stack development fundamentals.'
        ],
        'skills_highlight' => [
            'Backend Development',
            'API Development',
            'Database Design',
            'Web Applications',
        ],
        'years_experience' => \Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28')),
        'months_experience' => \Carbon\Carbon::now()->diffInMonths(\Carbon\Carbon::parse('2017-05-28')) - \Carbon\Carbon::now()->diffInYears(\Carbon\Carbon::parse('2017-05-28')) * 12,
        'projects_completed' => 26,
        'happy_clients' => 15,
        'footer' => "Zahir Hayrullah - A Syrian-born backend developer thriving in Berlin since 2022. Passionate about transforming complex challenges into elegant digital solutions. Believes in the power of clean code to build the future, one application at a time.",
    ],

    /*
    |--------------------------------------------------------------------------
    | Services Section Configuration
    |--------------------------------------------------------------------------
    */
    'services' => [
        'title' => 'Services',
        'subtitle' => 'What I can do for you',
        'description' => 'I specialize in building robust, scalable backend solutions that power modern businesses. From enterprise-grade APIs to complex database architectures, I deliver technology solutions that drive growth and innovation.',
        'items' => [
            [
                'icon' => 'code',
                'title' => 'Enterprise Web Applications',
                'description' => 'Architecting and developing high-performance web applications that scale with your business. From concept to deployment, I create solutions that are secure, maintainable, and built for growth.',
                'features' => ['Laravel Ecosystem', 'Microservices Architecture', 'Cloud-Native Solutions'],
            ],
            [
                'icon' => 'api',
                'title' => 'API Architecture & Development',
                'description' => 'Designing and implementing robust API ecosystems that enable seamless integration and data flow between systems. Specializing in RESTful services and modern API patterns.',
                'features' => ['RESTful APIs', 'GraphQL Implementation', 'API Gateway Solutions'],
            ],
            [
                'icon' => 'database',
                'title' => 'Database Architecture & Optimization',
                'description' => 'Creating high-performance database solutions that handle complex queries and large datasets efficiently. From schema design to performance tuning, ensuring your data layer scales seamlessly.',
                'features' => ['Advanced Schema Design', 'Performance Optimization', 'Data Migration Strategies'],
            ],
            [
                'icon' => 'cms',
                'title' => 'Custom CMS Development',
                'description' => 'Building tailored content management systems that empower teams to manage digital content efficiently. User-friendly interfaces combined with powerful backend capabilities.',
                'features' => ['Headless CMS Solutions', 'Multi-tenant Architecture', 'Advanced Content Workflows'],
            ],
            [
                'icon' => 'crm',
                'title' => 'CRM & Business Systems',
                'description' => 'Developing comprehensive customer relationship management systems that streamline business processes and enhance customer engagement through intelligent automation.',
                'features' => ['Customer Journey Mapping', 'Automated Workflows', 'Advanced Analytics'],
            ],
            [
                'icon' => 'management',
                'title' => 'Industry-Specific Solutions',
                'description' => 'Creating specialized management systems tailored to specific industry requirements. From healthcare to education, delivering solutions that understand your unique challenges.',
                'features' => ['Healthcare Management', 'Educational Platforms', 'Hospitality Solutions'],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Experience Section Configuration
    |--------------------------------------------------------------------------
    */
    'experience' => [
        'title' => 'Work Experience',
        'subtitle' => 'My Professional Journey',
        'description' => 'Over 7 years of progressive experience in backend development, from junior developer to senior architect. I\'ve contributed to diverse projects across multiple industries, building scalable solutions that drive business growth.',
        'items' => [
            [
                'title' => 'PHP Developer',
                'company' => 'Content Fleet',
                'company_url' => 'https://contentfleet.com',
                'location' => 'Berlin, Germany',
                'type' => 'Full-time',
                'start_date' => '2023-06-01',
                'end_date' => null,
                'current' => true,
                'duration' => 'June 2023 - Present',
                'description' => 'Leading backend development initiatives for a cutting-edge content management platform. Architecting scalable API solutions and implementing advanced content workflow systems.',
                'responsibilities' => [
                    'Architecting and developing high-performance content management APIs serving millions of requests',
                    'Leading technical decision-making for backend infrastructure and database optimization',
                    'Implementing advanced content workflow automation systems using Laravel and modern PHP practices',
                    'Mentoring junior developers and establishing coding standards and best practices',
                    'Collaborating with cross-functional teams to deliver feature-rich content solutions',
                    'Optimizing database performance and implementing efficient caching strategies'
                ],
                'technologies' => ['PHP', 'Laravel', 'MySQL', 'Redis', 'Docker', 'AWS', 'Git'],
                'achievements' => [
                    'Improved API response times by 40% through database optimization and caching implementation',
                    'Successfully migrated legacy systems to modern Laravel architecture',
                    'Established automated testing practices resulting in 95% code coverage'
                ]
            ],
            [
                'title' => 'CRM Developer',
                'company' => 'Imtilak Group',
                'company_url' => 'https://www.imtilakgroup.com',
                'location' => 'Istanbul, Turkey',
                'type' => 'Full-time',
                'start_date' => '2020-01-01',
                'end_date' => '2022-06-10',
                'current' => false,
                'duration' => 'January 2020 - June 2022',
                'description' => 'Developed comprehensive CRM solutions for a diversified conglomerate spanning real estate, tourism, healthcare, education, and e-commerce sectors.',
                'responsibilities' => [
                    'Designed and implemented multi-tenant CRM systems serving 12+ business verticals',
                    'Developed custom modules for real estate management, customer tracking, and sales automation',
                    'Created integrated reporting dashboards providing real-time business insights',
                    'Built API integrations connecting CRM with external services and third-party platforms',
                    'Implemented role-based access control and security measures for sensitive business data',
                    'Collaborated with business stakeholders to translate requirements into technical solutions'
                ],
                'technologies' => ['PHP', 'Laravel', 'MySQL', 'JavaScript', 'Vue.js', 'Bootstrap', 'Git'],
                'achievements' => [
                    'Successfully delivered CRM solutions for 12 different business verticals',
                    'Reduced manual data entry by 60% through automation and workflow optimization',
                    'Implemented real-time reporting system improving decision-making efficiency'
                ]
            ],
            [
                'title' => 'Web Developer',
                'company' => 'Imtilak Real Estate',
                'company_url' => 'https://imtilak.net',
                'location' => 'Istanbul, Turkey',
                'type' => 'Full-time',
                'start_date' => '2019-08-05',
                'end_date' => '2020-01-01',
                'current' => false,
                'duration' => 'August 2019 - January 2020',
                'description' => 'Focused on developing and maintaining real estate web applications, property management systems, and customer-facing portals.',
                'responsibilities' => [
                    'Developed property listing and management systems with advanced search capabilities',
                    'Created responsive web interfaces for property browsing and customer inquiries',
                    'Implemented property image galleries and virtual tour integrations',
                    'Built customer portal for property favorites, inquiries, and communication',
                    'Maintained and optimized existing web applications for performance',
                    'Collaborated with design team to implement pixel-perfect UI components'
                ],
                'technologies' => ['PHP', 'Laravel', 'MySQL', 'JavaScript', 'jQuery', 'HTML5', 'CSS3'],
                'achievements' => [
                    'Developed comprehensive property management system handling 1000+ listings',
                    'Improved website loading speed by 35% through code optimization',
                    'Successfully integrated multiple third-party real estate APIs'
                ]
            ],
            [
                'title' => 'Developer',
                'company' => 'Binoplus',
                'company_url' => null,
                'location' => 'Istanbul, Turkey',
                'type' => 'Full-time',
                'start_date' => '2018-06-25',
                'end_date' => '2019-06-25',
                'current' => false,
                'duration' => 'June 2018 - June 2019',
                'description' => 'Started my professional journey as a junior developer, gaining hands-on experience in web development and learning industry best practices.',
                'responsibilities' => [
                    'Developed and maintained web applications using PHP and modern frameworks',
                    'Participated in code reviews and learned software development best practices',
                    'Collaborated with senior developers on feature implementation and bug fixes',
                    'Created responsive user interfaces and implemented frontend functionality',
                    'Assisted in database design and optimization tasks',
                    'Contributed to testing and quality assurance processes'
                ],
                'technologies' => ['PHP', 'MySQL', 'JavaScript', 'HTML5', 'CSS3', 'Bootstrap'],
                'achievements' => [
                    'Successfully completed junior developer training program',
                    'Contributed to 5+ web application projects',
                    'Gained foundational experience in full-stack development'
                ]
            ]
        ],
        'show_company_logos' => true,
        'show_technologies' => true,
        'show_achievements' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Education Section Configuration
    |--------------------------------------------------------------------------
    */
    'education' => [
        'title' => 'Education & Certifications',
        'subtitle' => 'Academic Background & Professional Development',
        'description' => 'My educational foundation in Computer Engineering combined with continuous professional development through certifications and specialized training programs.',
        'items' => [
            [
                'title' => 'Bachelor of Computer Engineering',
                'institution' => 'Sakarya University',
                'location' => 'Sakarya, Turkey',
                'degree' => 'Bachelor\'s Degree',
                'field' => 'Computer Engineering',
                'start_date' => '2015-10-14',
                'end_date' => '2019-08-12',
                'duration' => '2015 - 2019',
                'gpa' => null,
                'description' => 'Completed a comprehensive 4-year Computer Engineering program focusing on computer architecture, programming languages, software engineering, and network engineering. Gained expertise in system analysis, software development methodologies, and technology consulting.',
                'coursework' => [
                    'Data Structures and Algorithms',
                    'Database Management Systems',
                    'Software Engineering Principles',
                    'Computer Networks and Security',
                    'Web Development Technologies',
                    'Object-Oriented Programming',
                    'System Analysis and Design',
                    'Computer Architecture'
                ],
                'projects' => [
                    'Developed a comprehensive student management system using PHP and MySQL',
                    'Created a network monitoring application for campus infrastructure',
                    'Built a web-based inventory management system as final year project'
                ]
            ],
            [
                'title' => 'High School Diploma',
                'institution' => 'Istiklal High School',
                'location' => 'Gaziantep, Turkey',
                'degree' => 'High School Diploma',
                'field' => 'General Studies',
                'start_date' => '2013-10-14',
                'end_date' => '2014-08-12',
                'duration' => '2013 - 2014',
                'gpa' => 'High Grades',
                'description' => 'Successfully completed high school education with excellent academic performance, establishing a strong educational foundation for university studies.',
                'achievements' => [
                    'Graduated with high academic honors',
                    'Developed strong analytical and problem-solving skills',
                    'Participated in mathematics and science competitions'
                ]
            ]
        ],
        'certifications' => [
            [
                'title' => 'PHP & MySQL Web Development',
                'provider' => 'Udemy',
                'date' => '2020',
                'credential_id' => null,
                'url' => null,
                'description' => 'Comprehensive course covering advanced PHP programming and MySQL database management'
            ],
            [
                'title' => 'React.js Fundamentals',
                'provider' => 'Udemy',
                'date' => '2021',
                'credential_id' => null,
                'url' => null,
                'description' => 'Frontend development course focusing on React.js components and modern JavaScript'
            ],
            [
                'title' => 'Ubuntu Server Essentials',
                'provider' => 'Udemy',
                'date' => '2020',
                'credential_id' => null,
                'url' => null,
                'description' => 'Linux server administration and deployment strategies'
            ],
            [
                'title' => 'Building RESTful Serverless APIs',
                'provider' => 'Coursera',
                'date' => '2021',
                'credential_id' => null,
                'url' => null,
                'description' => 'Advanced API development and serverless architecture patterns'
            ],
            [
                'title' => 'Flutter Mobile App Development',
                'provider' => 'Coursera',
                'date' => '2021',
                'credential_id' => null,
                'url' => null,
                'description' => 'Cross-platform mobile application development using Flutter framework'
            ]
        ],
        'show_coursework' => true,
        'show_projects' => true,
        'show_certifications' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Skills Section Configuration
    |--------------------------------------------------------------------------
    */
    'skills' => [
        'title' => 'Technical Skills',
        'subtitle' => 'Technologies & Expertise',
        'description' => 'My technical expertise spans across modern backend technologies, database systems, and development tools. I continuously evolve my skill set to leverage cutting-edge technologies that deliver exceptional results.',
        'categories' => [
            'backend' => [
                'title' => 'Backend Development',
                'description' => 'Server-side technologies and frameworks',
                'skills' => [
                    ['name' => 'PHP', 'level' => 95, 'years' => 7, 'icon' => 'php'],
                    ['name' => 'Laravel', 'level' => 95, 'years' => 6, 'icon' => 'laravel'],
                    ['name' => 'C#', 'level' => 85, 'years' => 4, 'icon' => 'csharp'],
                    ['name' => '.NET', 'level' => 85, 'years' => 4, 'icon' => 'dotnet'],
                ]
            ],
            'frontend' => [
                'title' => 'Frontend Development',
                'description' => 'Client-side technologies and frameworks',
                'skills' => [
                    ['name' => 'JavaScript', 'level' => 90, 'years' => 6, 'icon' => 'javascript'],
                    ['name' => 'Vue.js', 'level' => 85, 'years' => 4, 'icon' => 'vuedotjs'],
                    ['name' => 'jQuery', 'level' => 90, 'years' => 6, 'icon' => 'jquery'],
                    ['name' => 'HTML5', 'level' => 95, 'years' => 7, 'icon' => 'html5'],
                    ['name' => 'CSS3', 'level' => 90, 'years' => 7, 'icon' => 'css3'],
                ]
            ],
            'database' => [
                'title' => 'Database Systems',
                'description' => 'Database design and management',
                'skills' => [
                    ['name' => 'MySQL', 'level' => 95, 'years' => 7, 'icon' => 'mysql'],
                    ['name' => 'PostgreSQL', 'level' => 85, 'years' => 4, 'icon' => 'postgresql'],
                    ['name' => 'SQL Server', 'level' => 80, 'years' => 3, 'icon' => 'microsoftsqlserver'],
                    ['name' => 'SQLite', 'level' => 85, 'years' => 5, 'icon' => 'sqlite'],
                ]
            ],
            'tools' => [
                'title' => 'Development Tools',
                'description' => 'IDEs, version control, and productivity tools',
                'skills' => [
                    ['name' => 'PhpStorm', 'level' => 95, 'years' => 6, 'icon' => 'phpstorm'],
                    ['name' => 'Git', 'level' => 90, 'years' => 6, 'icon' => 'git'],
                    ['name' => 'GitHub', 'level' => 90, 'years' => 6, 'icon' => 'github'],
                    ['name' => 'GitLab', 'level' => 85, 'years' => 4, 'icon' => 'gitlab'],
                    ['name' => 'Bitbucket', 'level' => 80, 'years' => 3, 'icon' => 'bitbucket'],
                ]
            ]
        ],
        'soft_skills' => [
            ['name' => 'Problem Solving', 'description' => 'Analytical thinking and creative solution development'],
            ['name' => 'Team Leadership', 'description' => 'Leading development teams and mentoring junior developers'],
            ['name' => 'Project Management', 'description' => 'Agile methodologies and project delivery'],
            ['name' => 'Communication', 'description' => 'Technical documentation and stakeholder collaboration'],
            ['name' => 'Continuous Learning', 'description' => 'Staying updated with latest technologies and best practices'],
            ['name' => 'Code Review', 'description' => 'Ensuring code quality and knowledge sharing'],
        ],
        'show_experience_years' => true,
        'show_proficiency_levels' => true,
        'show_contact_info' => true,
        'show_soft_skills' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Resume Section Configuration
    |--------------------------------------------------------------------------
    */
    'resume' => [
        'title' => 'Resume',
        'subtitle' => 'My Professional Background',
        'description' => 'A comprehensive overview of my professional journey, educational background, and technical expertise in backend development.',
        'show_download_button' => true,
        'download_url' => '/resume/zahir-hayrullah-resume.pdf',
        'sections' => [
            'summary' => [
                'title' => 'Professional Summary',
                'content' => 'Senior Backend Developer with 7+ years of experience building scalable web applications and API solutions. Expertise in PHP, Laravel, and modern development practices. Proven track record of leading technical teams and delivering high-performance systems for diverse industries including real estate, content management, and e-commerce.',
                'highlights' => [
                    '7+ years of backend development experience',
                    'Expert in PHP, Laravel, and modern web technologies',
                    'Led development teams and mentored junior developers',
                    'Built scalable systems serving millions of requests',
                    'Strong background in API development and database optimization'
                ]
            ],
            'experience' => [
                'title' => 'Professional Experience',
                'show_details' => true,
                'show_technologies' => true,
                'show_achievements' => true
            ],
            'education' => [
                'title' => 'Education',
                'show_coursework' => false,
                'show_projects' => false,
                'show_certifications' => true
            ],
            'skills' => [
                'title' => 'Technical Skills',
                'show_categories' => true,
                'show_proficiency' => true,
                'featured_skills' => [
                    'PHP/Laravel',
                    'JavaScript/Vue.js',
                    'MySQL/PostgreSQL',
                    'Git/GitHub',
                    'API Development',
                    'Database Design'
                ]
            ]
        ],
        'contact_info' => [
            'show_on_resume' => true,
            'items' => [
                'email' => '<EMAIL>',
                'phone' => '+90 ************',
                'location' => 'Berlin, Germany',
                'website' => 'zaherr.com',
                'linkedin' => 'linkedin.com/in/zaherkhirullah',
                'github' => 'github.com/zaherkhirullah'
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Portfolio Section Configuration
    |--------------------------------------------------------------------------
    */
    'portfolio' => [
        'title' => 'My Portfolio',
        'subtitle' => 'Recent projects I\'ve worked on',
        'categories' => [
            'all' => 'All Projects',
            'web' => 'Web Development',
            'mobile' => 'Mobile Apps',
            'design' => 'UI/UX Design',
        ],
        'items_per_page' => 6,
        'show_filters' => true,
        'show_modal' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Contact Section Configuration
    |--------------------------------------------------------------------------
    */
    'contact' => [
        'title' => 'Contact Me',
        'subtitle' => 'Get In Touch',
        'description' => 'Ready to bring your next project to life? Let\'s discuss how we can transform your ideas into powerful digital solutions. I\'m always excited to tackle new challenges and collaborate on innovative projects.',
        'email' => '<EMAIL>',
        'email_link' => 'mailto:<EMAIL>',
        'phone' => '+90 ************',
        'phone_link' => 'tel:+4915114634111',
        'phone_whatsapp_link' => 'https://api.whatsapp.com/send?phone=4915114634111',
        'address' => 'Berlin, Germany',
        'zipcode' => '10115',
        'website' => 'zaherr.com',
        'website_link' => 'https://zaherr.com',
        'show_contact_form' => true,
        'show_map' => false,
        'map_coordinates' => [
            'lat' => 52.5200,
            'lng' => 13.4050,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Color Scheme
    |--------------------------------------------------------------------------
    */
    'colors' => [
        'primary' => '#3b82f6',
        'secondary' => '#64748b',
        'accent' => '#f59e0b',
        'success' => '#10b981',
        'warning' => '#f59e0b',
        'error' => '#ef4444',
        'background' => '#ffffff',
        'surface' => '#f8fafc',
        'text' => '#1e293b',
    ],

    /*
    |--------------------------------------------------------------------------
    | Typography
    |--------------------------------------------------------------------------
    */
    'typography' => [
        'font_family' => 'Inter',
        'heading_font' => 'Inter',
        'body_font' => 'Inter',
        'font_sizes' => [
            'xs' => '0.75rem',
            'sm' => '0.875rem',
            'base' => '1rem',
            'lg' => '1.125rem',
            'xl' => '1.25rem',
            '2xl' => '1.5rem',
            '3xl' => '1.875rem',
            '4xl' => '2.25rem',
            '5xl' => '3rem',
            '6xl' => '3.75rem',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Layout Configuration
    |--------------------------------------------------------------------------
    */
    'layout' => [
        'container_width' => '1200px',
        'sidebar_width' => '300px',
        'header_height' => '80px',
        'footer_height' => '200px',
        'section_padding' => '80px',
        'grid_gap' => '2rem',
    ],

    /*
    |--------------------------------------------------------------------------
    | Animation Settings
    |--------------------------------------------------------------------------
    */
    'animations' => [
        'enabled' => true,
        'duration' => 800,
        'easing' => 'ease-in-out',
        'delay' => 100,
        'offset' => 100,
        'disable_on_mobile' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | SEO Configuration
    |--------------------------------------------------------------------------
    */
    'seo' => [
        'meta_title_template' => '%s | Zahir Hayrullah - Senior Backend Developer',
        'meta_description_default' => 'Zahir Hayrullah - Senior Backend Developer specializing in Laravel, API development, and scalable web applications. Based in Berlin, Germany.',
        'meta_keywords_default' => 'backend developer, Laravel expert, API development, web applications, Berlin developer, PHP developer',
        'og_image_default' => 'images/og-image.jpg',
        'twitter_card_type' => 'summary_large_image',
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'lazy_loading' => true,
        'image_optimization' => true,
        'css_minification' => true,
        'js_minification' => true,
        'gzip_compression' => true,
        'cache_duration' => 3600, // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Media Links
    |--------------------------------------------------------------------------
    */
    'social_links' => [
        'github' => 'https://github.com/zaherkhirullah',
        'linkedin' => 'https://linkedin.com/in/zaherkhirullah',
        'twitter' => 'https://twitter.com/zaherkhirullah',
        'instagram' => 'https://instagram.com/zaherkhirullah',
        'facebook' => 'https://facebook.com/zaherkhirullah',
        'gitlab' => '',
        'sourcerer' => 'https://sourcerer.io/zaherkhirullah',
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom CSS/JS
    |--------------------------------------------------------------------------
    */
    'custom' => [
        'css' => '',
        'js' => '',
        'head_scripts' => '',
        'body_scripts' => '',
    ],
];
