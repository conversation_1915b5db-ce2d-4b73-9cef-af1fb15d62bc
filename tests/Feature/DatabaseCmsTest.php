<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\Experience;
use App\Models\Technology;
use App\Models\Education;
use App\Models\Certification;
use App\Services\SeoService;

class DatabaseCmsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed test data
        $this->artisan('db:seed', ['--class' => 'TechnologySeeder']);
        $this->artisan('db:seed', ['--class' => 'ExperienceSeeder']);
        $this->artisan('db:seed', ['--class' => 'EducationSeeder']);
        $this->artisan('db:seed', ['--class' => 'CertificationSeeder']);
    }

    /** @test */
    public function skills_page_loads_successfully()
    {
        $response = $this->get('/skills');

        $response->assertStatus(200);
        $response->assertViewIs('themes.modern.pages.skills');
        $response->assertViewHas(['technologies', 'categories', 'seoData', 'breadcrumbs']);
    }

    /** @test */
    public function skills_api_returns_correct_data()
    {
        $response = $this->getJson('/api/skills');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'technologies',
                'categories',
                'featured'
            ]
        ]);
    }

    /** @test */
    public function services_page_loads_successfully()
    {
        $response = $this->get('/services');

        $response->assertStatus(200);
        $response->assertViewIs('themes.modern.pages.services');
        $response->assertViewHas(['services', 'categories', 'seoData', 'breadcrumbs']);
    }

    /** @test */
    public function about_page_loads_successfully()
    {
        $response = $this->get('/about-me');

        $response->assertStatus(200);
        $response->assertViewIs('themes.modern.pages.about');
        $response->assertViewHas([
            'aboutData',
            'featuredExperiences',
            'featuredTechnologies',
            'careerStats',
            'seoData',
            'breadcrumbs'
        ]);
    }

    /** @test */
    public function contact_page_loads_successfully()
    {
        $response = $this->get('/contact-me');

        $response->assertStatus(200);
        $response->assertViewIs('themes.modern.pages.contact');
        $response->assertViewHas(['contactData', 'seoData', 'breadcrumbs']);
    }

    /** @test */
    public function experience_page_loads_successfully()
    {
        $response = $this->get('/experience');

        $response->assertStatus(200);
        $response->assertViewIs('themes.modern.pages.experience');
        $response->assertViewHas([
            'experiences',
            'companies',
            'technologies',
            'years',
            'totalExperience',
            'seoData',
            'breadcrumbs'
        ]);
    }

    /** @test */
    public function education_page_loads_successfully()
    {
        $response = $this->get('/education');

        $response->assertStatus(200);
        $response->assertViewIs('themes.modern.pages.education');
        $response->assertViewHas([
            'education',
            'certifications',
            'certificationsByProvider',
            'seoData',
            'breadcrumbs'
        ]);
    }

    /** @test */
    public function experience_model_relationships_work()
    {
        $experience = Experience::factory()->create();
        $technology = Technology::factory()->create();

        $experience->technologies()->attach($technology->id, [
            'proficiency_gained' => 'advanced',
            'is_primary' => true
        ]);

        $this->assertTrue($experience->technologies->contains($technology));
        $this->assertTrue($technology->experiences->contains($experience));
    }

    /** @test */
    public function technology_scopes_work_correctly()
    {
        Technology::factory()->create(['is_active' => true, 'is_featured' => true]);
        Technology::factory()->create(['is_active' => false, 'is_featured' => false]);

        $activeTechnologies = Technology::active()->get();
        $featuredTechnologies = Technology::featured()->get();

        $this->assertCount(1, $activeTechnologies);
        $this->assertCount(1, $featuredTechnologies);
    }

    /** @test */
    public function seo_service_generates_correct_data()
    {
        $seoService = new SeoService();

        $seoData = $seoService->generateSeoData([
            'title' => 'Test Page Title',
            'description' => 'Test page description',
            'keywords' => 'test, keywords'
        ]);

        $this->assertArrayHasKey('title', $seoData);
        $this->assertArrayHasKey('description', $seoData);
        $this->assertArrayHasKey('og_title', $seoData);
        $this->assertArrayHasKey('twitter_title', $seoData);
        $this->assertEquals('Test Page Title', $seoData['title']);
    }

    /** @test */
    public function breadcrumb_structured_data_is_generated()
    {
        $seoService = new SeoService();

        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Skills', 'url' => url('/skills')],
            ['name' => 'PHP', 'url' => null]
        ];

        $structuredData = $seoService->generateBreadcrumbStructuredData($breadcrumbs);

        $this->assertEquals('https://schema.org', $structuredData['@context']);
        $this->assertEquals('BreadcrumbList', $structuredData['@type']);
        $this->assertCount(3, $structuredData['itemListElement']);
    }

    /** @test */
    public function contact_form_validation_works()
    {
        $response = $this->post('/contact-me', []);

        $response->assertSessionHasErrors(['name', 'email', 'subject', 'message']);
    }

    /** @test */
    public function contact_form_submission_works()
    {
        $formData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject',
            'message' => 'Test message content',
            'phone' => '+1234567890',
            'company' => 'Test Company'
        ];

        $response = $this->post('/contact-me', $formData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('messages', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'subject' => 'Test Subject'
        ]);
    }

    /** @test */
    public function api_endpoints_return_correct_structure()
    {
        $endpoints = [
            '/api/skills',
            '/api/services',
            '/api/experience',
            '/api/education'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint);

            $response->assertStatus(200);
            $response->assertJsonStructure([
                'success',
                'data'
            ]);
            $response->assertJson(['success' => true]);
        }
    }

    /** @test */
    public function experience_filtering_works()
    {
        $response = $this->getJson('/api/experience?company=Content+Fleet');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'experiences',
                'total',
                'companies',
                'technologies',
                'years'
            ]
        ]);
    }

    /** @test */
    public function theme_helper_functions_work()
    {
        $this->assertTrue(function_exists('theme_config_get'));
        $this->assertTrue(function_exists('theme_view'));
        $this->assertTrue(function_exists('theme_asset'));

        $heroConfig = theme_config_get('hero');
        $this->assertIsArray($heroConfig);

        $viewPath = theme_view('pages.skills');
        $this->assertStringContains('themes.modern.pages.skills', $viewPath);
    }
}
