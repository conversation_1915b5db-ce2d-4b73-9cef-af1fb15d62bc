const cacheName = 'ZAHIR-CV-v20250604024037'
const staticAssets = [
  '/',
  '/about-us',
  '/skills',
  '/experience',
  '/contact-us',
  '/education'
]

// Call Install Event
self.addEventListener('install', e => {
  console.log('Service Worker: Installed')
  e.waitUntil(
    caches.open(cacheName).then(cache => {
      console.log('Service Worker: Caching static assets')
      // Cache assets individually to handle failures gracefully
      return Promise.allSettled(
        staticAssets.map(url =>
          fetch(url).then(response => {
            if (response.ok) {
              return cache.put(url, response)
            } else {
              console.warn(`Service Worker: Failed to fetch ${url}, status: ${response.status}`)
              return Promise.resolve()
            }
          }).catch(err => {
            console.warn(`Service Worker: Network error for ${url}:`, err)
            return Promise.resolve()
          })
        )
      ).then(results => {
        const successful = results.filter(result => result.status === 'fulfilled').length
        const failed = results.filter(result => result.status === 'rejected').length
        console.log(`Service Worker: Cached ${successful} assets, ${failed} failed`)
      })
    }).catch(err => {
      console.error('Service Worker: Failed to open cache', err)
    })
  )
  self.skipWaiting()
})

// Call Activate Event
self.addEventListener('activate', e => {
  console.log('Service Worker: Activated')
  e.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cache => {
          if (cache !== cacheName) {
            console.log('Service Worker: Deleting old cache', cache)
            return caches.delete(cache)
          }
        })
      )
    })
  )
  self.clients.claim()
})

// Call Fetch Event
self.addEventListener('fetch', e => {
  // Only handle GET requests
  if (e.request.method !== 'GET') {
    return
  }

  // Skip caching for certain requests
  try {
    const url = new URL(e.request.url)
    const skipCache = [
      '/api/',
      '/admin/',
      '/livewire/',
      '/telescope/',
      '/horizon/',
      'chrome-extension:',
      'moz-extension:',
      'safari-extension:'
    ]

    if (skipCache.some(path => url.pathname.includes(path) || url.protocol.includes('extension'))) {
      return
    }

    // Skip requests with query parameters (except for static assets)
    if (url.search && !url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2)$/)) {
      return
    }
  } catch (err) {
    console.warn('Service Worker: Invalid URL', e.request.url, err)
    return
  }

  e.respondWith(
    caches.match(e.request).then(cachedResponse => {
      // Return cached response if available
      if (cachedResponse) {
        return cachedResponse
      }

      // Fetch from network
      return fetch(e.request).then(response => {
        // Check if response is valid
        if (!response || response.status !== 200 || response.type !== 'basic') {
          return response
        }

        // Clone the response
        const responseToCache = response.clone()

        // Cache the response
        caches.open(cacheName).then(cache => {
          // Only cache certain types of requests
          const cacheableTypes = ['document', 'script', 'style', 'image', 'font']
          const destination = e.request.destination || 'document'

          if (cacheableTypes.includes(destination)) {
            cache.put(e.request, responseToCache).catch(err => {
              console.warn('Service Worker: Failed to cache request', e.request.url, err)
            })
          }
        }).catch(err => {
          console.warn('Service Worker: Failed to open cache', err)
        })

        return response
      }).catch(err => {
        console.warn('Service Worker: Network request failed', e.request.url, err)
        // Return cached response if network fails
        return caches.match(e.request).then(cachedResponse => {
          return cachedResponse || new Response('Offline', {
            status: 503,
            statusText: 'Service Unavailable'
          })
        })
      })
    })
  )
})
