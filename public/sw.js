// const cacheName = 'ZAHIR'

// // Call Install Event
// self.addEventListener('install', e => {
//   console.log('Service Worker: Installed')
// })

// // Call Activate Event
// self.addEventListener('activate', e => {
//   e.waitUntil(
//     caches.keys().then(cacheNames => {
//       return Promise.all(
//         cacheNames.map(cache => {
//           if (cache !== cacheName) {
//             return caches.delete(cache)
//           }
//         })
//       )
//     })
//   )
// })

// // Call Fetch Event
// self.addEventListener('fetch', e => {
//   if(e.request.method === 'GET') {
//     e.respondWith(
//       fetch(e.request)
//       .then(res => {
//         const resClone = res.clone()
//         caches.open(cacheName).then(cache => {
//           if(!(e.request.url.indexOf('http') === 0)){
//             return
//           }
//           cache.put(e.request, resClone)
//         })
//         return res
//       }).catch(err => caches.match(e.request).then(res => res))
//     )
//   }

// })
