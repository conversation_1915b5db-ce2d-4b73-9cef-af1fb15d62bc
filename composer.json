{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1|^8.2|^8.3", "hayrullah/laravel-helpers": "dev-master", "jeroennoten/laravel-adminlte": "^3.9.4", "laravel/framework": "^10.0", "laravel/tinker": "^2.8", "maatwebsite/excel": "*", "mcamara/laravel-localization": "^1.8", "silviolleite/laravelpwa": "^2.0.3", "spatie/laravel-sitemap": "^6.4", "torann/geoip": "^3.0.5", "yajra/laravel-datatables": "^10.0", "yajra/laravel-datatables-oracle": "^10.0"}, "require-dev": {"fakerphp/faker": "^1.21", "laravel/sail": "^1.25", "laravel/ui": "^4.6", "mockery/mockery": "^1.6", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeders", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}