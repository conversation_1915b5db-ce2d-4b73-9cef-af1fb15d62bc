# Database-Driven CV Application

A comprehensive, database-driven content management system for a professional CV/portfolio website built with Laravel 10.x and modern web technologies.

## 🚀 Features

### ✅ Implemented Features

#### Database-Driven Content Management
- **Dynamic Experience Management**: Work history with technology relationships
- **Skills & Technologies**: Categorized technical skills with proficiency levels
- **Education & Certifications**: Academic background and professional credentials
- **Services Portfolio**: Professional services with detailed descriptions

#### Individual Page System
- **Skills Page** (`/skills`): Interactive technology showcase with filtering
- **Services Page** (`/services`): Professional services with category filtering
- **About Page** (`/about-us`): Personal story with career statistics
- **Contact Page** (`/contact-us`): Working contact form with validation
- **Experience Page** (`/experience`): Work history with advanced filtering
- **Education Page** (`/education`): Academic background and certifications

#### SEO Optimization
- **Dynamic Meta Tags**: Page-specific titles, descriptions, and keywords
- **Structured Data**: Schema.org markup for enhanced search results
- **Open Graph Tags**: Social media sharing optimization
- **XML Sitemap**: Automatic sitemap generation
- **Breadcrumb Navigation**: SEO-friendly navigation structure

#### API-First Architecture
- **RESTful APIs**: Complete API coverage for all content types
- **Advanced Filtering**: Multi-parameter filtering for experiences and skills
- **JSON Responses**: Standardized API response format
- **Performance Optimized**: Cached queries and optimized database access

## 🛠 Installation & Setup

### Prerequisites
- PHP 8.1+
- Laravel 10.x
- MySQL 8.0+ or PostgreSQL 13+
- Composer
- Node.js & NPM

### Installation Steps

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd newCV
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database Setup**
   ```bash
   # Configure database in .env file
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=newcv
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

5. **Run Migrations & Seeders**
   ```bash
   php artisan migrate
   php artisan db:seed --class=TechnologySeeder
   php artisan db:seed --class=ExperienceSeeder
   php artisan db:seed --class=EducationSeeder
   php artisan db:seed --class=CertificationSeeder
   ```

6. **Build Assets**
   ```bash
   npm run build
   ```

7. **Start Development Server**
   ```bash
   php artisan serve
   ```

## 📁 Project Structure

### Controllers
```
app/Http/Controllers/
├── AboutController.php          # About page with career stats
├── ContactController.php        # Contact form and messaging
├── ExperienceController.php     # Work experience management
├── FrontendEducationController.php # Education and certifications
├── ServicesController.php       # Professional services
├── SkillsController.php         # Technical skills (existing)
├── SitemapController.php        # SEO sitemap generation
└── HomeController.php           # Home page (existing)
```

### Models
```
app/Models/
├── Experience.php               # Work experience with relationships
├── Technology.php               # Technical skills and categories
├── Education.php                # Academic background
├── Certification.php            # Professional certifications
├── Page.php                     # CMS pages (existing)
└── Message.php                  # Contact form messages
```

### Services
```
app/Services/
├── SeoService.php               # SEO meta tags and structured data
└── ThemeService.php             # Theme management (existing)
```

### Database
```
database/
├── migrations/
│   ├── 2024_01_01_000001_create_experiences_table.php
│   ├── 2024_01_01_000002_create_technologies_table.php
│   ├── 2024_01_01_000003_create_certifications_table.php
│   ├── 2024_01_01_000004_create_experience_technology_table.php
│   ├── 2024_01_01_000005_create_education_technology_table.php
│   └── 2024_01_01_000006_add_slug_to_experiences_table.php
└── seeders/
    ├── ExperienceSeeder.php
    ├── TechnologySeeder.php
    ├── EducationSeeder.php
    └── CertificationSeeder.php
```

## 🔗 API Endpoints

### Individual Page APIs
```
GET /api/skills                 # Skills with filtering
GET /api/services               # Services with categories
GET /api/experience             # Experience with filtering
GET /api/education              # Education and certifications
```

### API Parameters
```
# Skills API
GET /api/skills?category=backend&proficiency=5

# Experience API
GET /api/experience?company=Content+Fleet&technology=Laravel&year=2023

# Services API
GET /api/services?category=web-development
```

### Response Format
```json
{
    "success": true,
    "data": {
        "items": [...],
        "total": 10,
        "filters": {
            "categories": [...],
            "companies": [...],
            "technologies": [...]
        }
    }
}
```

## 🎨 Frontend Integration

### Theme System
The application uses a modular theme system with the modern theme as default:

```php
// Get theme configuration
$heroConfig = theme_config_get('hero');

// Generate theme asset URLs
$cssUrl = theme_asset('css/app.css');

// Get theme view path
$viewPath = theme_view('pages.skills');
```

### Blade Components
```blade
{{-- SEO Meta Tags --}}
@include('themes.modern.partials.seo-meta', ['seoData' => $seoData])

{{-- Breadcrumb Navigation --}}
@include('themes.modern.partials.breadcrumbs', ['breadcrumbs' => $breadcrumbs])

{{-- Structured Data --}}
@include('themes.modern.partials.structured-data', ['structuredData' => $seoData['structured_data']])
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --filter=DatabaseCmsTest

# Run with coverage
php artisan test --coverage
```

### Test Coverage
- **Feature Tests**: Page rendering and API endpoints
- **Unit Tests**: Model relationships and helper functions
- **Integration Tests**: Database operations and SEO functionality

## 🚀 Deployment

### Production Setup
1. **Environment Configuration**
   ```bash
   APP_ENV=production
   APP_DEBUG=false
   APP_URL=https://your-domain.com
   ```

2. **Database Optimization**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

3. **Asset Optimization**
   ```bash
   npm run production
   ```

4. **SEO Setup**
   - Submit sitemap to Google Search Console: `/sitemap.xml`
   - Configure robots.txt: `/robots.txt`
   - Verify structured data: `/structured-data.json`

## 📊 Performance Features

### Caching Strategy
- **Database Caching**: Eloquent model caching for frequently accessed data
- **Query Optimization**: Eager loading and optimized database queries
- **Asset Versioning**: Automatic cache busting for theme assets
- **Page Caching**: Full page caching for static content

### SEO Optimization
- **Dynamic Meta Tags**: Page-specific SEO optimization
- **Structured Data**: Rich snippets for enhanced search results
- **Canonical URLs**: Duplicate content prevention
- **Mobile-First Design**: Responsive and mobile-optimized

## 🔧 Configuration

### Theme Configuration
```php
// config/themes/modern.php
return [
    'hero' => [...],
    'about' => [...],
    'services' => [...],
    'contact' => [...],
];
```

### SEO Configuration
```php
// In controllers
$seoData = $this->seoService->generateSeoData([
    'title' => 'Page Title',
    'description' => 'Page description',
    'keywords' => 'relevant, keywords',
    'canonical' => url('/current-page'),
]);
```

## 📝 Documentation

- **Implementation Guide**: `docs/database-cms-implementation.md`
- **Theme System**: `docs/theme-system.md`
- **API Documentation**: Available at `/api/documentation`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- GitHub Issues: Create an issue in the repository
- Documentation: Check the `docs/` directory

---

**Built with ❤️ by Zahir Hayrullah**
