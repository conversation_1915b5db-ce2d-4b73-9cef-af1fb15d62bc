<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ThemeController;
use App\Http\Controllers\Api\ResumeController;
use App\Http\Controllers\SkillsController;
use App\Http\Controllers\ServicesController;
use App\Http\Controllers\ExperienceController;
use App\Http\Controllers\FrontendEducationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| Theme API Routes
|--------------------------------------------------------------------------
*/
Route::prefix('theme')->group(function () {
    Route::get('/', [ThemeController::class, 'index']);
    Route::get('/current', [ThemeController::class, 'current']);
    Route::post('/switch', [ThemeController::class, 'switch']);
    Route::post('/toggle-dark-mode', [ThemeController::class, 'toggleDarkMode']);
    Route::get('/config', [ThemeController::class, 'config']);
    Route::get('/supports', [ThemeController::class, 'supports']);
    Route::get('/{theme}/metadata', [ThemeController::class, 'metadata']);
    Route::get('/{theme}/assets', [ThemeController::class, 'assets']);
    Route::delete('/cache', [ThemeController::class, 'clearCache']);
});

/*
|--------------------------------------------------------------------------
| Resume API Routes
|--------------------------------------------------------------------------
*/
Route::prefix('resume')->group(function () {
    Route::get('/', [ResumeController::class, 'index']);
    Route::get('/experience', [ResumeController::class, 'experience']);
    Route::get('/education', [ResumeController::class, 'education']);
    Route::get('/skills', [ResumeController::class, 'skills']);
    Route::get('/summary', [ResumeController::class, 'summary']);
    Route::get('/search', [ResumeController::class, 'search']);
    Route::get('/experience/{index}', [ResumeController::class, 'getExperience']);
    Route::get('/education/{index}', [ResumeController::class, 'getEducation']);
});

/*
|--------------------------------------------------------------------------
| Individual Page API Routes
|--------------------------------------------------------------------------
*/
Route::prefix('api')->group(function () {
    Route::get('/skills', [SkillsController::class, 'api']);
    Route::get('/services', [ServicesController::class, 'api']);
    Route::get('/experience', [ExperienceController::class, 'api']);
    Route::get('/education', [FrontendEducationController::class, 'api']);
    Route::get('/skills/{category}', [ResumeController::class, 'getSkillsByCategory']);
});
