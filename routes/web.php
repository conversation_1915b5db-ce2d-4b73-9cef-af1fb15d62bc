<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\SkillsController;
use App\Http\Controllers\ServicesController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ExperienceController;
use App\Http\Controllers\FrontendEducationController;
use App\Http\Controllers\ResumeController;
use App\Http\Controllers\SitemapController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Home page
Route::get('/', [HomeController::class, 'index'])->name('home');

// Individual pages with SEO-friendly URLs
Route::get('/skills', [SkillsController::class, 'index'])->name('skills.index');
Route::get('/skills/{slug}', [SkillsController::class, 'show'])->name('skills.show');

Route::get('/services', [ServicesController::class, 'index'])->name('services.index');
Route::get('/services/{slug}', [ServicesController::class, 'show'])->name('services.show');

Route::get('/about-us', [AboutController::class, 'index'])->name('about.index');

Route::get('/contact-us', [ContactController::class, 'index'])->name('contact.index');
Route::post('/contact-us', [ContactController::class, 'store'])->name('contact.store');

Route::get('/experience', [ExperienceController::class, 'index'])->name('experience.index');
Route::get('/experience/{slug}', [ExperienceController::class, 'show'])->name('experience.show');

Route::get('/education', [FrontendEducationController::class, 'index'])->name('education.index');

Route::get('/resume', [ResumeController::class, 'index'])->name('resume.index');
Route::get('/resume/download', [ResumeController::class, 'download'])->name('resume.download');

// Blog routes (if needed)
Route::get('/blog', [HomeController::class, 'blog'])->name('blog.index');

// SEO routes
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', [SitemapController::class, 'robots'])->name('robots');
Route::get('/structured-data.json', [SitemapController::class, 'structuredData'])->name('structured-data');

// Legacy routes for backward compatibility
Route::get('/home', [HomeController::class, 'home']);
Route::get('/{slug}', [HomeController::class, 'page'])->name('page.show');
