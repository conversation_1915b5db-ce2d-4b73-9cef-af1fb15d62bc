<?php

/*
|--------------------------------------------------------------------------
| AMP Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Support\Facades\Route;

Route::prefix('amp')->as('amp')->group(function () {
    Route::get('/', 'AmpController@index')->name('index');
    Route::get('skills', 'AmpController@skills')->name('skills');
    Route::get('about-me', 'Amp<PERSON><PERSON>roller@about_us')->name('about_us');
    Route::get('contact-me', 'Amp<PERSON><PERSON>roller@contact_us')->name('contact_us');
    Route::get('blog', 'AmpController@blog')->name('blog.index');
    //    Route::get('{slug}', 'AmpController@page')->name('page.index');
    //    Route::post('sent/contact', 'AmpController@storeMessage')->name('send.contact.message');
    //Route::get('/sitemap', 'AmpController@sitemap')->name('sitemap');

});
