<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Response;

class PageSpeedOptimizationService
{
    /**
     * Generate critical CSS for above-the-fold content
     */
    public function generateCriticalCss(): string
    {
        return Cache::remember('critical_css', 3600, function () {
            return $this->extractCriticalCss();
        });
    }

    /**
     * Extract critical CSS from main stylesheet
     */
    private function extractCriticalCss(): string
    {
        $criticalCss = "
        /* Critical CSS for above-the-fold content */
        html, body { margin: 0; padding: 0; font-family: 'Inter', sans-serif; }
        .hero-section { min-height: 100vh; display: flex; align-items: center; }
        .navbar { position: fixed; top: 0; width: 100%; z-index: 1000; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
        .text-primary { color: #3b82f6; }
        .bg-primary { background-color: #3b82f6; }
        .btn { padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #3b82f6; color: white; }
        .btn-primary:hover { background-color: #2563eb; }
        .fade-in { opacity: 0; animation: fadeIn 0.6s ease-in-out forwards; }
        @keyframes fadeIn { to { opacity: 1; } }
        .loading-skeleton { background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: loading 1.5s infinite; }
        @keyframes loading { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }
        ";

        return $this->minifyCss($criticalCss);
    }

    /**
     * Minify CSS content
     */
    public function minifyCss(string $css): string
    {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove unnecessary whitespace
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Remove whitespace around specific characters
        $css = preg_replace('/\s*([{}:;,>+~])\s*/', '$1', $css);
        
        // Remove trailing semicolon before closing brace
        $css = preg_replace('/;(?=\s*})/', '', $css);
        
        return trim($css);
    }

    /**
     * Minify JavaScript content
     */
    public function minifyJs(string $js): string
    {
        // Remove single-line comments (but preserve URLs)
        $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);
        
        // Remove multi-line comments
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Remove unnecessary whitespace
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Remove whitespace around operators and punctuation
        $js = preg_replace('/\s*([{}();,=+\-*\/])\s*/', '$1', $js);
        
        return trim($js);
    }

    /**
     * Generate resource hints for performance
     */
    public function generateResourceHints(): array
    {
        return [
            'dns-prefetch' => [
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com',
            ],
            'preconnect' => [
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com',
            ],
            'preload' => [
                [
                    'href' => 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap',
                    'as' => 'style',
                    'crossorigin' => 'anonymous'
                ]
            ]
        ];
    }

    /**
     * Generate performance headers
     */
    public function getPerformanceHeaders(): array
    {
        return [
            'Cache-Control' => 'public, max-age=31536000, immutable',
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'camera=(), microphone=(), geolocation=()',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains; preload',
        ];
    }

    /**
     * Optimize images for web delivery
     */
    public function optimizeImage(string $imagePath): array
    {
        $fullPath = public_path($imagePath);
        
        if (!File::exists($fullPath)) {
            return ['optimized' => false, 'note' => 'File not found'];
        }

        $originalSize = File::size($fullPath);
        $extension = strtolower(File::extension($fullPath));
        
        // For now, just return file info
        // In production, you'd use image optimization libraries like Intervention Image
        return [
            'optimized' => true,
            'original_size' => $originalSize,
            'optimized_size' => $originalSize, // Would be smaller after optimization
            'savings' => 0, // Would show actual savings
            'format' => $extension
        ];
    }

    /**
     * Generate lazy loading attributes for images
     */
    public function getLazyLoadingAttributes(bool $isAboveFold = false): array
    {
        if ($isAboveFold) {
            return [
                'loading' => 'eager',
                'decoding' => 'async'
            ];
        }

        return [
            'loading' => 'lazy',
            'decoding' => 'async'
        ];
    }

    /**
     * Generate WebP fallback for images
     */
    public function generateWebPFallback(string $imagePath): array
    {
        $pathInfo = pathinfo($imagePath);
        $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
        
        return [
            'webp' => $webpPath,
            'fallback' => $imagePath,
            'picture_element' => [
                'sources' => [
                    ['srcset' => $webpPath, 'type' => 'image/webp'],
                ],
                'img' => ['src' => $imagePath, 'alt' => '']
            ]
        ];
    }

    /**
     * Calculate Core Web Vitals thresholds
     */
    public function getCoreWebVitalsThresholds(): array
    {
        return [
            'LCP' => [
                'good' => 2.5,      // seconds
                'needs_improvement' => 4.0,
                'poor' => 'above 4.0'
            ],
            'FID' => [
                'good' => 100,      // milliseconds
                'needs_improvement' => 300,
                'poor' => 'above 300'
            ],
            'CLS' => [
                'good' => 0.1,      // score
                'needs_improvement' => 0.25,
                'poor' => 'above 0.25'
            ],
            'FCP' => [
                'good' => 1.8,      // seconds
                'needs_improvement' => 3.0,
                'poor' => 'above 3.0'
            ],
            'TTI' => [
                'good' => 3.8,      // seconds
                'needs_improvement' => 7.3,
                'poor' => 'above 7.3'
            ]
        ];
    }

    /**
     * Generate performance budget recommendations
     */
    public function getPerformanceBudget(): array
    {
        return [
            'total_page_size' => '1.5MB',
            'javascript_budget' => '300KB',
            'css_budget' => '100KB',
            'image_budget' => '1MB',
            'font_budget' => '100KB',
            'third_party_budget' => '200KB',
            'requests_budget' => 50,
            'dom_elements_budget' => 1500,
        ];
    }

    /**
     * Check if resource should be preloaded
     */
    public function shouldPreload(string $resourceType, string $url): bool
    {
        $preloadCriteria = [
            'font' => fn($url) => str_contains($url, 'fonts.googleapis.com'),
            'style' => fn($url) => str_contains($url, 'app.css') || str_contains($url, 'critical'),
            'script' => fn($url) => str_contains($url, 'app.js'),
            'image' => fn($url) => str_contains($url, 'hero') || str_contains($url, 'avatar'),
        ];

        return isset($preloadCriteria[$resourceType]) && 
               $preloadCriteria[$resourceType]($url);
    }

    /**
     * Generate service worker cache strategy
     */
    public function getCacheStrategy(string $resourceType): string
    {
        $strategies = [
            'page' => 'CacheFirst',
            'api' => 'NetworkFirst', 
            'image' => 'CacheFirst',
            'font' => 'CacheFirst',
            'css' => 'StaleWhileRevalidate',
            'js' => 'StaleWhileRevalidate',
        ];

        return $strategies[$resourceType] ?? 'NetworkFirst';
    }
}
