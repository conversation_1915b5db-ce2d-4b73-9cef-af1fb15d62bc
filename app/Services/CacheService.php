<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

class CacheService
{
    /**
     * Cache duration in minutes
     */
    const CACHE_DURATION = 60; // 1 hour
    const LONG_CACHE_DURATION = 1440; // 24 hours
    const SHORT_CACHE_DURATION = 15; // 15 minutes

    /**
     * Cache keys
     */
    const CACHE_KEYS = [
        'technologies_active' => 'technologies.active',
        'technologies_featured' => 'technologies.featured',
        'experiences_active' => 'experiences.active',
        'experiences_featured' => 'experiences.featured',
        'education_active' => 'education.active',
        'certifications_active' => 'certifications.active',
        'certifications_featured' => 'certifications.featured',
        'pages_active' => 'pages.active',
        'site_settings' => 'site.settings',
        'navigation_menu' => 'navigation.menu',
    ];

    /**
     * Get cached data or execute callback and cache result
     */
    public function remember(string $key, callable $callback, int $minutes = null): mixed
    {
        $minutes = $minutes ?? self::CACHE_DURATION;

        return Cache::remember($key, now()->addMinutes($minutes), $callback);
    }

    /**
     * Get cached technologies
     */
    public function getTechnologies(bool $activeOnly = true, bool $featuredOnly = false): Collection
    {
        $key = $featuredOnly ? self::CACHE_KEYS['technologies_featured'] : self::CACHE_KEYS['technologies_active'];

        return $this->remember($key, function () use ($activeOnly, $featuredOnly) {
            $query = \App\Models\Technology::query();

            if ($activeOnly) {
                $query->active();
            }

            if ($featuredOnly) {
                $query->featured();
            }

            return $query->ordered()->get();
        }, self::LONG_CACHE_DURATION);
    }

    /**
     * Get cached experiences
     */
    public function getExperiences(bool $activeOnly = true, bool $featuredOnly = false): Collection
    {
        $key = $featuredOnly ? self::CACHE_KEYS['experiences_featured'] : self::CACHE_KEYS['experiences_active'];

        return $this->remember($key, function () use ($activeOnly, $featuredOnly) {
            $query = \App\Models\Experience::with(['technologies']);

            if ($activeOnly) {
                $query->active();
            }

            if ($featuredOnly) {
                $query->featured();
            }

            return $query->ordered()->get();
        }, self::CACHE_DURATION);
    }

    /**
     * Get cached education
     */
    public function getEducation(bool $activeOnly = true): Collection
    {
        return $this->remember(self::CACHE_KEYS['education_active'], function () use ($activeOnly) {
            $query = \App\Models\Education::with(['technologies']);

            if ($activeOnly) {
                $query->active();
            }

            return $query->ordered()->get();
        }, self::LONG_CACHE_DURATION);
    }

    /**
     * Get cached certifications
     */
    public function getCertifications(bool $activeOnly = true, bool $featuredOnly = false): Collection
    {
        $key = $featuredOnly ? self::CACHE_KEYS['certifications_featured'] : self::CACHE_KEYS['certifications_active'];

        return $this->remember($key, function () use ($activeOnly, $featuredOnly) {
            $query = \App\Models\Certification::query();

            if ($activeOnly) {
                $query->active();
            }

            if ($featuredOnly) {
                $query->featured();
            }

            return $query->ordered()->get();
        }, self::LONG_CACHE_DURATION);
    }

    /**
     * Get cached pages
     */
    public function getPages(bool $activeOnly = true): Collection
    {
        return $this->remember(self::CACHE_KEYS['pages_active'], function () use ($activeOnly) {
            // Check if Page model exists
            if (!class_exists(\App\Models\Page::class)) {
                return collect([]);
            }

            $query = \App\Models\Page::query();

            if ($activeOnly && \Illuminate\Support\Facades\Schema::hasColumn('pages', 'is_active')) {
                $query->where('is_active', true);
            }

            return $query->orderBy('sort_order')->get();
        }, self::LONG_CACHE_DURATION);
    }

    /**
     * Get cached site settings
     */
    public function getSiteSettings(): array
    {
        return $this->remember(self::CACHE_KEYS['site_settings'], function () {
            return [
                'site_name' => config('app.name'),
                'site_description' => 'Professional portfolio of Zahir Hayrullah - Senior Backend Developer',
                'site_keywords' => 'Zahir Hayrullah, backend developer, PHP, Laravel, web development',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+49 ************',
                'location' => 'Berlin, Germany',
                'social_links' => [
                    'linkedin' => 'https://linkedin.com/in/zaherkhirullah',
                    'github' => 'https://github.com/zaherkhirullah',
                    'website' => 'https://zaherr.com'
                ]
            ];
        }, self::LONG_CACHE_DURATION);
    }

    /**
     * Clear specific cache
     */
    public function forget(string $key): bool
    {
        return Cache::forget($key);
    }

    /**
     * Clear all application cache
     */
    public function clearAll(): void
    {
        foreach (self::CACHE_KEYS as $key) {
            $this->forget($key);
        }
    }

    /**
     * Clear cache for specific model
     */
    public function clearModelCache(string $model): void
    {
        $modelCacheKeys = [
            'Technology' => ['technologies_active', 'technologies_featured'],
            'Experience' => ['experiences_active', 'experiences_featured'],
            'Education' => ['education_active'],
            'Certification' => ['certifications_active', 'certifications_featured'],
            'Page' => ['pages_active'],
        ];

        if (isset($modelCacheKeys[$model])) {
            foreach ($modelCacheKeys[$model] as $cacheKey) {
                $this->forget(self::CACHE_KEYS[$cacheKey]);
            }
        }
    }

    /**
     * Warm up cache
     */
    public function warmUp(): void
    {
        $this->getTechnologies();
        $this->getTechnologies(true, true);
        $this->getExperiences();
        $this->getExperiences(true, true);
        $this->getEducation();
        $this->getCertifications();
        $this->getCertifications(true, true);
        $this->getSiteSettings();
    }
}
