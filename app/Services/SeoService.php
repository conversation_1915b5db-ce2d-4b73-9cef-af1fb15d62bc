<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\URL;

class SeoService
{
    /**
     * Generate SEO data for pages
     */
    public function generateSeoData(array $data): array
    {
        $defaults = [
            'title' => config('app.name', '<PERSON><PERSON><PERSON>'),
            'description' => 'Professional backend developer specializing in PHP, Laravel, and web development. Based in Berlin, Germany.',
            'keywords' => '<PERSON><PERSON><PERSON>, backend developer, PHP, Laravel, web development, Berlin',
            'canonical' => URL::current(),
            'og_type' => 'website',
            'og_site_name' => config('app.name', '<PERSON><PERSON><PERSON>'),
            'og_locale' => 'en_US',
            'twitter_card' => 'summary_large_image',
            'twitter_site' => '@zaherkhirullah',
            'robots' => 'index, follow',
            'author' => 'Zahir Hayrullah',
            'structured_data' => null,
        ];

        $seoData = array_merge($defaults, $data);

        // Ensure title is not too long
        if (strlen($seoData['title']) > 60) {
            $seoData['title'] = substr($seoData['title'], 0, 57) . '...';
        }

        // Ensure description is not too long
        if (strlen($seoData['description']) > 160) {
            $seoData['description'] = substr($seoData['description'], 0, 157) . '...';
        }

        // Add Open Graph data
        $seoData['og_title'] = $seoData['title'];
        $seoData['og_description'] = $seoData['description'];
        $seoData['og_url'] = $seoData['canonical'];
        $seoData['og_image'] = $seoData['og_image'] ?? asset('images/default-og.jpg');

        // Add Twitter Card data
        $seoData['twitter_title'] = $seoData['title'];
        $seoData['twitter_description'] = $seoData['description'];
        $seoData['twitter_image'] = $seoData['og_image'];

        return $seoData;
    }

    /**
     * Generate breadcrumb structured data
     */
    public function generateBreadcrumbStructuredData(array $breadcrumbs): array
    {
        $itemListElement = [];

        foreach ($breadcrumbs as $index => $breadcrumb) {
            $itemListElement[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url'] ?? URL::current(),
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $itemListElement,
        ];
    }

    /**
     * Generate website structured data
     */
    public function generateWebsiteStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => 'Zahir Hayrullah - Backend Developer',
            'description' => 'Professional portfolio and resume of Zahir Hayrullah, a senior backend developer specializing in PHP, Laravel, and web development.',
            'url' => url('/'),
            'author' => [
                '@type' => 'Person',
                'name' => 'Zahir Hayrullah',
                'jobTitle' => 'Senior Backend Developer',
                'email' => '<EMAIL>',
                'url' => url('/about-me'),
            ],
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => url('/search?q={search_term_string}'),
                'query-input' => 'required name=search_term_string',
            ],
            'sameAs' => [
                'https://linkedin.com/in/zaherkhirullah',
                'https://github.com/zaherkhirullah',
                'https://twitter.com/zaherkhirullah',
            ],
        ];
    }

    /**
     * Generate organization structured data
     */
    public function generateOrganizationStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'ProfessionalService',
            'name' => 'Zahir Hayrullah - Web Development Services',
            'description' => 'Professional web development services specializing in backend development, API integration, and custom web applications.',
            'url' => url('/'),
            'logo' => asset('images/logo.png'),
            'image' => asset('images/zahir-profile.jpg'),
            'founder' => [
                '@type' => 'Person',
                'name' => 'Zahir Hayrullah',
                'jobTitle' => 'Senior Backend Developer',
            ],
            'address' => [
                '@type' => 'PostalAddress',
                'addressLocality' => 'Berlin',
                'addressCountry' => 'Germany',
            ],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => '+90 ************',
                'contactType' => 'customer service',
                'email' => '<EMAIL>',
                'availableLanguage' => ['English', 'Arabic', 'Turkish'],
            ],
            'sameAs' => [
                'https://linkedin.com/in/zaherkhirullah',
                'https://github.com/zaherkhirullah',
            ],
            'serviceType' => [
                'Web Development',
                'Backend Development',
                'API Development',
                'Database Design',
                'PHP Development',
                'Laravel Development',
            ],
            'areaServed' => [
                'Berlin',
                'Germany',
                'Europe',
                'Remote Worldwide',
            ],
        ];
    }

    /**
     * Generate FAQ structured data
     */
    public function generateFaqStructuredData(array $faqs): array
    {
        $mainEntity = [];

        foreach ($faqs as $faq) {
            $mainEntity[] = [
                '@type' => 'Question',
                'name' => $faq['question'],
                'acceptedAnswer' => [
                    '@type' => 'Answer',
                    'text' => $faq['answer'],
                ],
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => $mainEntity,
        ];
    }

    /**
     * Generate sitemap data
     */
    public function generateSitemapData(): array
    {
        $urls = [
            ['url' => url('/'), 'priority' => 1.0, 'changefreq' => 'weekly'],
            ['url' => url('/about-me'), 'priority' => 0.9, 'changefreq' => 'monthly'],
            ['url' => url('/skills'), 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['url' => url('/services'), 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['url' => url('/experience'), 'priority' => 0.7, 'changefreq' => 'monthly'],
            ['url' => url('/education'), 'priority' => 0.6, 'changefreq' => 'yearly'],
            ['url' => url('/contact-me'), 'priority' => 0.7, 'changefreq' => 'monthly'],
            ['url' => url('/resume'), 'priority' => 0.8, 'changefreq' => 'monthly'],
        ];

        return $urls;
    }

    /**
     * Cache SEO data for performance
     */
    public function cacheSeoData(string $key, array $data, int $minutes = 1440): void
    {
        Cache::put("seo_data_{$key}", $data, now()->addMinutes($minutes));
    }

    /**
     * Get cached SEO data
     */
    public function getCachedSeoData(string $key): ?array
    {
        return Cache::get("seo_data_{$key}");
    }
}
