<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class AssetService
{
    /**
     * Get versioned asset URL with cache busting
     */
    public function asset(string $path): string
    {
        $fullPath = public_path($path);
        
        if (!File::exists($fullPath)) {
            return asset($path);
        }
        
        // Get file modification time for cache busting
        $version = $this->getAssetVersion($fullPath);
        
        return asset($path) . '?v=' . $version;
    }

    /**
     * Get theme asset with versioning
     */
    public function themeAsset(string $path, string $theme = 'modern'): string
    {
        $themePath = "themes/{$theme}/{$path}";
        return $this->asset($themePath);
    }

    /**
     * Get asset version (file modification time hash)
     */
    private function getAssetVersion(string $fullPath): string
    {
        $cacheKey = 'asset_version_' . md5($fullPath);
        
        return Cache::remember($cacheKey, 3600, function () use ($fullPath) {
            return substr(md5(filemtime($fullPath)), 0, 8);
        });
    }

    /**
     * Generate critical CSS for above-the-fold content
     */
    public function getCriticalCss(string $page = 'default'): string
    {
        $cacheKey = "critical_css_{$page}";
        
        return Cache::remember($cacheKey, 1440, function () use ($page) {
            $criticalCssPath = resource_path("css/critical/{$page}.css");
            
            if (File::exists($criticalCssPath)) {
                return File::get($criticalCssPath);
            }
            
            // Return default critical CSS
            return $this->getDefaultCriticalCss();
        });
    }

    /**
     * Get default critical CSS
     */
    private function getDefaultCriticalCss(): string
    {
        return '
            /* Critical CSS for above-the-fold content */
            body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
            .header { position: fixed; top: 0; left: 0; right: 0; z-index: 40; background: rgba(255,255,255,0.8); backdrop-filter: blur(12px); }
            .container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
            .hero { min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .text-primary { color: #3b82f6; }
            .bg-primary { background-color: #3b82f6; }
            .hidden { display: none; }
            @media (min-width: 768px) { .md\\:block { display: block; } .md\\:hidden { display: none; } }
        ';
    }

    /**
     * Preload critical assets
     */
    public function getPreloadAssets(): array
    {
        return [
            [
                'rel' => 'preload',
                'href' => $this->themeAsset('css/app.css'),
                'as' => 'style'
            ],
            [
                'rel' => 'preload',
                'href' => $this->themeAsset('js/app.js'),
                'as' => 'script'
            ],
            [
                'rel' => 'preload',
                'href' => asset('fonts/inter-var.woff2'),
                'as' => 'font',
                'type' => 'font/woff2',
                'crossorigin' => 'anonymous'
            ]
        ];
    }

    /**
     * Get DNS prefetch domains
     */
    public function getDnsPrefetchDomains(): array
    {
        return [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'https://unpkg.com'
        ];
    }

    /**
     * Generate resource hints
     */
    public function getResourceHints(): array
    {
        return [
            'dns-prefetch' => $this->getDnsPrefetchDomains(),
            'preload' => $this->getPreloadAssets(),
            'prefetch' => [
                [
                    'href' => url('/about-us'),
                    'as' => 'document'
                ],
                [
                    'href' => url('/contact-us'),
                    'as' => 'document'
                ]
            ]
        ];
    }

    /**
     * Minify CSS content
     */
    public function minifyCss(string $css): string
    {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove unnecessary whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        $css = preg_replace('/\s+/', ' ', $css);
        $css = str_replace(['; ', ' {', '{ ', ' }', '} ', ': ', ', '], [';', '{', '{', '}', '}', ':', ','], $css);
        
        return trim($css);
    }

    /**
     * Minify JavaScript content
     */
    public function minifyJs(string $js): string
    {
        // Basic JS minification (for production, use a proper minifier)
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js); // Remove multi-line comments
        $js = preg_replace('/\/\/.*$/m', '', $js); // Remove single-line comments
        $js = preg_replace('/\s+/', ' ', $js); // Replace multiple spaces with single space
        $js = str_replace(['; ', ' {', '{ ', ' }', '} '], [';', '{', '{', '}', '}'], $js);
        
        return trim($js);
    }

    /**
     * Generate integrity hash for assets
     */
    public function generateIntegrityHash(string $content): string
    {
        return 'sha384-' . base64_encode(hash('sha384', $content, true));
    }

    /**
     * Get asset with integrity hash
     */
    public function assetWithIntegrity(string $path): array
    {
        $fullPath = public_path($path);
        
        if (!File::exists($fullPath)) {
            return [
                'url' => asset($path),
                'integrity' => null
            ];
        }
        
        $content = File::get($fullPath);
        
        return [
            'url' => $this->asset($path),
            'integrity' => $this->generateIntegrityHash($content)
        ];
    }

    /**
     * Clear asset cache
     */
    public function clearAssetCache(): void
    {
        $keys = Cache::getRedis()->keys('*asset_version_*');
        foreach ($keys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
        }
        
        // Clear critical CSS cache
        $criticalKeys = Cache::getRedis()->keys('*critical_css_*');
        foreach ($criticalKeys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
        }
    }
}
