<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class AssetOptimizationService
{
    /**
     * Optimize and minify CSS
     */
    public function optimizeCss(string $content): string
    {
        // Remove comments
        $content = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $content);
        
        // Remove unnecessary whitespace
        $content = preg_replace('/\s+/', ' ', $content);
        
        // Remove whitespace around specific characters
        $content = str_replace(['; ', ' {', '{ ', ' }', '} ', ': ', ', '], [';', '{', '{', '}', '}', ':', ','], $content);
        
        // Remove trailing semicolons before closing braces
        $content = str_replace(';}', '}', $content);
        
        return trim($content);
    }

    /**
     * Optimize and minify JavaScript
     */
    public function optimizeJs(string $content): string
    {
        // Remove single-line comments
        $content = preg_replace('/\/\/.*$/m', '', $content);
        
        // Remove multi-line comments
        $content = preg_replace('/\/\*[\s\S]*?\*\//', '', $content);
        
        // Remove unnecessary whitespace
        $content = preg_replace('/\s+/', ' ', $content);
        
        // Remove whitespace around operators and brackets
        $content = str_replace(['; ', ' {', '{ ', ' }', '} ', ' = ', ' + ', ' - ', ' * ', ' / '], 
                              [';', '{', '{', '}', '}', '=', '+', '-', '*', '/'], $content);
        
        return trim($content);
    }

    /**
     * Generate critical CSS for above-the-fold content
     */
    public function generateCriticalCss(): string
    {
        return '
/* Critical CSS - Above the fold */
*,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
::before,::after{--tw-content:""}
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}
body{margin:0;line-height:inherit}
h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}
a{color:inherit;text-decoration:inherit}
button{font-family:inherit;font-size:100%;font-weight:inherit;line-height:inherit;color:inherit;margin:0;padding:0}
.container{width:100%;margin-left:auto;margin-right:auto;padding-left:1rem;padding-right:1rem}
@media (min-width:640px){.container{max-width:640px}}
@media (min-width:768px){.container{max-width:768px}}
@media (min-width:1024px){.container{max-width:1024px}}
@media (min-width:1280px){.container{max-width:1280px}}
.header{position:fixed;top:0;left:0;right:0;z-index:40;background:rgba(255,255,255,0.8);backdrop-filter:blur(12px)}
.hero{min-height:100vh;display:flex;align-items:center;justify-content:center}
.text-primary{color:#3b82f6}
.bg-primary{background-color:#3b82f6}
.hidden{display:none}
@media (min-width:768px){.md\\:block{display:block}.md\\:hidden{display:none}}
.flex{display:flex}
.items-center{align-items:center}
.justify-center{justify-content:center}
.text-center{text-align:center}
.font-bold{font-weight:700}
.text-4xl{font-size:2.25rem;line-height:2.5rem}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.mb-4{margin-bottom:1rem}
.mb-6{margin-bottom:1.5rem}
.px-4{padding-left:1rem;padding-right:1rem}
.py-2{padding-top:0.5rem;padding-bottom:0.5rem}
.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}
        ';
    }

    /**
     * Optimize images by converting to WebP format
     */
    public function optimizeImage(string $imagePath): array
    {
        $info = pathinfo($imagePath);
        $extension = strtolower($info['extension']);
        
        // Skip if already optimized format
        if (in_array($extension, ['webp', 'avif'])) {
            return [
                'original' => $imagePath,
                'webp' => $imagePath,
                'optimized' => true
            ];
        }
        
        $webpPath = $info['dirname'] . '/' . $info['filename'] . '.webp';
        
        // Check if WebP version already exists
        if (File::exists(public_path($webpPath))) {
            return [
                'original' => $imagePath,
                'webp' => $webpPath,
                'optimized' => true
            ];
        }
        
        // For now, return original path (WebP conversion would require image processing library)
        return [
            'original' => $imagePath,
            'webp' => $imagePath,
            'optimized' => false,
            'note' => 'WebP conversion requires image processing library (GD/Imagick)'
        ];
    }

    /**
     * Generate responsive image srcset
     */
    public function generateResponsiveImageSrcset(string $imagePath, array $sizes = [320, 640, 768, 1024, 1280]): string
    {
        $info = pathinfo($imagePath);
        $srcset = [];
        
        foreach ($sizes as $size) {
            $responsivePath = $info['dirname'] . '/' . $info['filename'] . '-' . $size . 'w.' . $info['extension'];
            $srcset[] = asset($responsivePath) . ' ' . $size . 'w';
        }
        
        return implode(', ', $srcset);
    }

    /**
     * Defer non-critical JavaScript
     */
    public function deferNonCriticalJs(string $html): string
    {
        // Add defer attribute to non-critical scripts
        $nonCriticalScripts = [
            'analytics',
            'gtag',
            'facebook',
            'twitter',
            'instagram',
            'linkedin'
        ];
        
        foreach ($nonCriticalScripts as $script) {
            $html = preg_replace(
                '/(<script[^>]*src[^>]*' . $script . '[^>]*)(>)/i',
                '$1 defer$2',
                $html
            );
        }
        
        return $html;
    }

    /**
     * Add lazy loading to images
     */
    public function addLazyLoading(string $html): string
    {
        // Add loading="lazy" to images that are not above the fold
        $html = preg_replace(
            '/(<img(?![^>]*loading=)[^>]*)(>)/i',
            '$1 loading="lazy"$2',
            $html
        );
        
        return $html;
    }

    /**
     * Optimize font loading
     */
    public function optimizeFontLoading(): array
    {
        return [
            'preload' => [
                [
                    'rel' => 'preload',
                    'href' => 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
                    'as' => 'style',
                    'onload' => "this.onload=null;this.rel='stylesheet'"
                ]
            ],
            'fallback' => [
                'font-family' => '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            ]
        ];
    }

    /**
     * Generate resource hints for performance
     */
    public function generateResourceHints(): array
    {
        return [
            'dns-prefetch' => [
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com',
                'https://cdn.jsdelivr.net'
            ],
            'preconnect' => [
                'https://fonts.googleapis.com',
                'https://fonts.gstatic.com'
            ],
            'preload' => [
                [
                    'href' => versioned_theme_asset('css/app.css'),
                    'as' => 'style'
                ],
                [
                    'href' => versioned_theme_asset('js/app.js'),
                    'as' => 'script'
                ]
            ]
        ];
    }

    /**
     * Reduce DOM size by removing unnecessary elements
     */
    public function optimizeDom(string $html): string
    {
        // Remove empty elements
        $html = preg_replace('/<(\w+)[^>]*>\s*<\/\1>/', '', $html);
        
        // Remove unnecessary div wrappers
        $html = preg_replace('/<div[^>]*>\s*(<[^>]+>[^<]*<\/[^>]+>)\s*<\/div>/', '$1', $html);
        
        // Remove redundant classes
        $html = preg_replace('/class="([^"]*)\s+\1([^"]*)"/', 'class="$1$2"', $html);
        
        return $html;
    }

    /**
     * Cache optimized assets
     */
    public function cacheOptimizedAsset(string $key, string $content, int $minutes = 1440): void
    {
        Cache::put("optimized_asset_{$key}", $content, now()->addMinutes($minutes));
    }

    /**
     * Get cached optimized asset
     */
    public function getCachedOptimizedAsset(string $key): ?string
    {
        return Cache::get("optimized_asset_{$key}");
    }
}
