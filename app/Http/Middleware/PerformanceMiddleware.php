<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class PerformanceMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): BaseResponse
    {
        $response = $next($request);

        // Only apply to HTML responses
        if (!$this->shouldOptimize($response)) {
            return $response;
        }

        // Add performance headers
        // $this->addPerformanceHeaders($response);

        // Optimize HTML content
        if ($response instanceof Response) {
            $content = $response->getContent();
            $optimizedContent = $this->optimizeHtml($content);
            $response->setContent($optimizedContent);
        }

        return $response;
    }

    /**
     * Check if response should be optimized
     */
    private function shouldOptimize(BaseResponse $response): bool
    {
        $contentType = $response->headers->get('Content-Type', '');

        return str_contains($contentType, 'text/html') ||
            (empty($contentType) && $response instanceof Response);
    }

    /**
     * Add performance-related headers
     */
    private function addPerformanceHeaders(BaseResponse $response): void
    {
        $headers = [
            // Security headers
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',

            // Performance headers
            'X-DNS-Prefetch-Control' => 'on',

            // Cache headers for static content
            'Vary' => 'Accept-Encoding',
            'Cache-Control' => 'public, max-age=31536000',

            // Compression headers
            'Content-Encoding' => 'gzip',
        ];

        // Add CSP header
        $csp = $this->generateContentSecurityPolicy();
        if ($csp) {
            $headers['Content-Security-Policy'] = $csp;
        }

        foreach ($headers as $key => $value) {
            $response->headers->set($key, $value);
        }
    }

    /**
     * Generate Content Security Policy
     */
    private function generateContentSecurityPolicy(): string
    {
        $policies = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net",
            "font-src 'self' https://fonts.gstatic.com data:",
            "img-src 'self' data: https: blob:",
            "connect-src 'self' https:",
            "media-src 'self'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'",
            "frame-ancestors 'none'",
            "upgrade-insecure-requests"
        ];

        return implode('; ', $policies);
    }

    /**
     * Optimize HTML content
     */
    private function optimizeHtml(string $html): string
    {
        if (app()->environment('production')) {
            // Minify HTML in production
            $html = $this->minifyHtml($html);
        }

        // Add resource hints
        $html = $this->addResourceHints($html);

        return $html;
    }

    /**
     * Minify HTML content
     */
    private function minifyHtml(string $html): string
    {
        // Remove HTML comments (except IE conditionals)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);

        // Remove unnecessary whitespace
        $html = preg_replace('/\s+/', ' ', $html);
        $html = preg_replace('/>\s+</', '><', $html);

        // Remove whitespace around block elements
        $blockElements = 'div|p|h[1-6]|ul|ol|li|section|article|header|footer|nav|main|aside';
        $html = preg_replace('/\s*(<\/?(?:' . $blockElements . ')[^>]*>)\s*/', '$1', $html);

        return trim($html);
    }

    /**
     * Add resource hints to HTML
     */
    private function addResourceHints(string $html): string
    {
        $resourceHints = $this->generateResourceHints();

        // Find the closing </head> tag and insert resource hints before it
        $headClosePos = strpos($html, '</head>');
        if ($headClosePos !== false) {
            $html = substr_replace($html, $resourceHints . '</head>', $headClosePos, 7);
        }

        return $html;
    }

    /**
     * Generate resource hints HTML
     */
    private function generateResourceHints(): string
    {
        $hints = [];

        // DNS prefetch
        $dnsPrefetchDomains = [
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net'
        ];

        foreach ($dnsPrefetchDomains as $domain) {
            $hints[] = "<link rel=\"dns-prefetch\" href=\"{$domain}\">";
        }

        // Preconnect to critical domains
        $hints[] = "<link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">";
        $hints[] = "<link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>";

        // Preload critical assets
        $criticalAssets = [
            [
                'href' => asset('themes/modern/css/app.css'),
                'as' => 'style'
            ],
            [
                'href' => asset('themes/modern/js/app.js'),
                'as' => 'script'
            ]
        ];

        foreach ($criticalAssets as $asset) {
            $attributes = [];
            foreach ($asset as $key => $value) {
                $attributes[] = "{$key}=\"{$value}\"";
            }
            $hints[] = "<link rel=\"preload\" " . implode(' ', $attributes) . ">";
        }

        return "\n" . implode("\n", $hints) . "\n";
    }
}
