<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\News;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AdminController extends Controller
{
    /**
     * AdminController constructor.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * @return Factory|View
     * @throws Exception
     */
    public function index()
    {
        //        $most_read_posts = Post::orderBy('visits', 'desc')->take(10)->get();
        //        $most_read_projects = Project::orderBy('visits', 'desc')->take(10)->get();
        //        $most_read_pages = Page::orderBy('visits', 'desc')->take(10)->get();
        //        $most_read_news = News::orderBy('visits', 'desc')->take(10)->get();
        return view('backend.index');
    }

    /**
     * @param Request $request
     * @param $model
     * @return mixed
     */
    public function refresh_model_locked(Request $request, $model)
    {
        $ClassName = '\\App\\' . $model;
        $row = $ClassName::findOrFail($request->id);
        // this function call from  trait lockPage
        $row->refresh_locked();
        if ($request->ajax()) {
            return response()->json('This page locked success until ' . Carbon::now()->addSecond(5), 200);
        }
        return true;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    function bulkDelete(Request $request)
    {
        if (isset($request->id) and isset($request->modelName)) {
            $id_array = $request->id;
            $ClassName = '\App\\' . $request->modelName;
            $result = $ClassName::whereIn('id', $id_array);
            if ($result->delete()) {
                return response()->json('This item was deleted successfully', 200);
            }
        }
        return response()->json('not found', 404);
    }


    /**
     * @param Request $request
     * @return string
     */
    public function upload_from_tiny(Request $request)
    {
        if ($request->folder)
            return helper_upload_from_tiny($request, $request->folder);
        else response()->json('Please select folder name');
    }
}
