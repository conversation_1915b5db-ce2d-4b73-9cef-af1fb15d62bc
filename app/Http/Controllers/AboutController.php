<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\SeoService;
use App\Models\Experience;
use App\Models\Technology;
use App\Models\Education;
use App\Models\Certification;

class AboutController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the about page
     */
    public function index(Request $request): View
    {
        // Get about data from theme config
        $aboutData = theme_config_get('about', []);
        
        // Get featured experiences from database
        $featuredExperiences = Experience::active()
            ->featured()
            ->ordered()
            ->limit(3)
            ->get();
        
        // Get featured technologies
        $featuredTechnologies = Technology::active()
            ->featured()
            ->ordered()
            ->limit(8)
            ->get();
        
        // Get latest education
        $latestEducation = Education::active()
            ->ordered()
            ->first();
        
        // Get recent certifications
        $recentCertifications = Certification::active()
            ->ordered()
            ->limit(4)
            ->get();
        
        // Calculate career statistics
        $careerStats = $this->calculateCareerStats();
        
        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => 'About Zahir Hayrullah - Senior Backend Developer in Berlin',
            'description' => 'Learn about Zahir Hayrullah, a passionate backend developer from Syria, now thriving in Berlin. Over 7 years of experience in PHP, Laravel, and web development.',
            'keywords' => 'Zahir Hayrullah, backend developer, PHP developer, Laravel expert, Berlin developer, Syrian developer, web development, software engineer',
            'canonical' => url('/about-us'),
            'og_type' => 'profile',
            'og_image' => asset('images/about-og.jpg'),
            'structured_data' => $this->generatePersonStructuredData(),
        ]);
        
        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'About Us', 'url' => null],
        ];
        
        // Track page visit
        $page = get_from_cache_by_slug('about-us');
        incrementVisits($request, $page);
        
        return view(theme_view('pages.about'), compact(
            'aboutData',
            'featuredExperiences',
            'featuredTechnologies',
            'latestEducation',
            'recentCertifications',
            'careerStats',
            'seoData',
            'breadcrumbs',
            'page'
        ));
    }
    
    /**
     * Calculate career statistics
     */
    private function calculateCareerStats(): array
    {
        $firstExperience = Experience::active()
            ->orderBy('start_date', 'asc')
            ->first();
        
        $totalExperience = $firstExperience 
            ? now()->diffInYears($firstExperience->start_date)
            : 0;
        
        return [
            'years_experience' => $totalExperience,
            'projects_completed' => theme_config_get('about.stats.projects_completed', 26),
            'happy_clients' => theme_config_get('about.stats.happy_clients', 15),
            'technologies_mastered' => Technology::active()->count(),
            'certifications_earned' => Certification::active()->count(),
        ];
    }
    
    /**
     * Generate structured data for person
     */
    private function generatePersonStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Person',
            'name' => 'Zahir Hayrullah',
            'jobTitle' => 'Senior Backend Developer',
            'description' => 'Passionate backend developer with over 7 years of experience in PHP, Laravel, and web development.',
            'url' => url('/about-us'),
            'image' => asset('images/zahir-profile.jpg'),
            'address' => [
                '@type' => 'PostalAddress',
                'addressLocality' => 'Berlin',
                'addressCountry' => 'Germany'
            ],
            'email' => '<EMAIL>',
            'telephone' => '+49 ************',
            'sameAs' => [
                'https://linkedin.com/in/zaherkhirullah',
                'https://github.com/zaherkhirullah',
                'https://zaherr.com'
            ],
            'knowsAbout' => Technology::active()
                ->featured()
                ->pluck('name')
                ->toArray(),
            'alumniOf' => [
                '@type' => 'EducationalOrganization',
                'name' => 'Sakarya University',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressLocality' => 'Sakarya',
                    'addressCountry' => 'Turkey'
                ]
            ],
            'worksFor' => [
                '@type' => 'Organization',
                'name' => 'Content Fleet',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressLocality' => 'Berlin',
                    'addressCountry' => 'Germany'
                ]
            ]
        ];
    }
    
    /**
     * Download resume
     */
    public function downloadResume()
    {
        $resumePath = public_path('downloads/zahir-hayrullah-resume.pdf');
        
        if (!file_exists($resumePath)) {
            abort(404, 'Resume not found');
        }
        
        return response()->download($resumePath, 'Zahir-Hayrullah-Resume.pdf');
    }
}
