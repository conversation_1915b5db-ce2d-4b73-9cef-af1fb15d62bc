<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Experience;
use App\Models\Technology;
use App\Models\Page;
use App\Services\SeoService;

class SitemapController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Generate XML sitemap
     */
    public function index(): Response
    {
        $urls = $this->generateSitemapUrls();

        $xml = $this->generateSitemapXml($urls);

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Generate sitemap URLs
     */
    private function generateSitemapUrls(): array
    {
        $urls = [];

        // Static pages
        $staticPages = [
            ['url' => url('/'), 'priority' => 1.0, 'changefreq' => 'weekly', 'lastmod' => now()],
            ['url' => url('/about-me'), 'priority' => 0.9, 'changefreq' => 'monthly', 'lastmod' => now()],
            ['url' => url('/skills'), 'priority' => 0.8, 'changefreq' => 'monthly', 'lastmod' => now()],
            ['url' => url('/services'), 'priority' => 0.8, 'changefreq' => 'monthly', 'lastmod' => now()],
            ['url' => url('/experience'), 'priority' => 0.7, 'changefreq' => 'monthly', 'lastmod' => now()],
            ['url' => url('/education'), 'priority' => 0.6, 'changefreq' => 'yearly', 'lastmod' => now()],
            ['url' => url('/contact-me'), 'priority' => 0.7, 'changefreq' => 'monthly', 'lastmod' => now()],
            ['url' => url('/resume'), 'priority' => 0.8, 'changefreq' => 'monthly', 'lastmod' => now()],
        ];

        $urls = array_merge($urls, $staticPages);

        // Dynamic experience pages
        $experiences = Experience::active()->get();
        foreach ($experiences as $experience) {
            if ($experience->slug) {
                $urls[] = [
                    'url' => url("/experience/{$experience->slug}"),
                    'priority' => 0.6,
                    'changefreq' => 'yearly',
                    'lastmod' => $experience->updated_at ?? $experience->created_at
                ];
            }
        }

        // Dynamic skill pages
        $technologies = Technology::active()->whereNotNull('slug')->get();
        foreach ($technologies as $technology) {
            $urls[] = [
                'url' => url("/skills/{$technology->slug}"),
                'priority' => 0.5,
                'changefreq' => 'yearly',
                'lastmod' => $technology->updated_at ?? $technology->created_at
            ];
        }

        // Dynamic pages from database
        $pages = Page::active()->get();
        foreach ($pages as $page) {
            $urls[] = [
                'url' => url("/{$page->slug}"),
                'priority' => 0.5,
                'changefreq' => 'monthly',
                'lastmod' => $page->updated_at ?? $page->created_at
            ];
        }

        return $urls;
    }

    /**
     * Generate XML sitemap content
     */
    private function generateSitemapXml(array $urls): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($url['url']) . '</loc>' . "\n";
            $xml .= '    <lastmod>' . $url['lastmod']->format('Y-m-d\TH:i:s\Z') . '</lastmod>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Generate robots.txt
     */
    public function robots(): Response
    {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Disallow: /admin/\n";
        $content .= "Disallow: /api/\n";
        $content .= "Disallow: /storage/\n";
        $content .= "\n";
        $content .= "Sitemap: " . url('/sitemap.xml') . "\n";

        return response($content, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'public, max-age=86400',
        ]);
    }

    /**
     * Generate JSON-LD structured data for the website
     */
    public function structuredData(): Response
    {
        $structuredData = [
            '@context' => 'https://schema.org',
            '@graph' => [
                // Website
                [
                    '@type' => 'WebSite',
                    '@id' => url('/') . '#website',
                    'url' => url('/'),
                    'name' => 'Zahir Hayrullah - Backend Developer',
                    'description' => 'Professional portfolio and resume of Zahir Hayrullah, a senior backend developer specializing in PHP, Laravel, and web development.',
                    'publisher' => [
                        '@id' => url('/') . '#person'
                    ],
                    'potentialAction' => [
                        '@type' => 'SearchAction',
                        'target' => url('/search?q={search_term_string}'),
                        'query-input' => 'required name=search_term_string'
                    ]
                ],

                // Person
                [
                    '@type' => 'Person',
                    '@id' => url('/') . '#person',
                    'name' => 'Zahir Hayrullah',
                    'givenName' => 'Zahir',
                    'familyName' => 'Hayrullah',
                    'jobTitle' => 'Senior Backend Developer',
                    'description' => 'Passionate backend developer with over 7 years of experience in PHP, Laravel, and web development.',
                    'url' => url('/about-me'),
                    'image' => asset('images/zahir-profile.jpg'),
                    'email' => '<EMAIL>',
                    'telephone' => '+90 ************',
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => 'Berlin',
                        'addressCountry' => 'Germany'
                    ],
                    'sameAs' => [
                        'https://linkedin.com/in/zaherkhirullah',
                        'https://github.com/zaherkhirullah',
                        'https://zaherr.com'
                    ],
                    'knowsAbout' => Technology::active()->featured()->pluck('name')->toArray(),
                    'alumniOf' => [
                        '@type' => 'EducationalOrganization',
                        'name' => 'Sakarya University',
                        'address' => [
                            '@type' => 'PostalAddress',
                            'addressLocality' => 'Sakarya',
                            'addressCountry' => 'Turkey'
                        ]
                    ]
                ],

                // Organization
                [
                    '@type' => 'ProfessionalService',
                    '@id' => url('/') . '#organization',
                    'name' => 'Zahir Hayrullah - Web Development Services',
                    'description' => 'Professional web development services specializing in backend development, API integration, and custom web applications.',
                    'url' => url('/'),
                    'logo' => asset('images/logo.png'),
                    'founder' => [
                        '@id' => url('/') . '#person'
                    ],
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => 'Berlin',
                        'addressCountry' => 'Germany'
                    ],
                    'contactPoint' => [
                        '@type' => 'ContactPoint',
                        'telephone' => '+90 ************',
                        'contactType' => 'customer service',
                        'email' => '<EMAIL>',
                        'availableLanguage' => ['English', 'Arabic', 'Turkish']
                    ],
                    'serviceType' => [
                        'Web Development',
                        'Backend Development',
                        'API Development',
                        'Database Design',
                        'PHP Development',
                        'Laravel Development'
                    ],
                    'areaServed' => [
                        'Berlin',
                        'Germany',
                        'Europe',
                        'Remote Worldwide'
                    ]
                ]
            ]
        ];

        return response()->json($structuredData, 200, [
            'Content-Type' => 'application/ld+json',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }
}
