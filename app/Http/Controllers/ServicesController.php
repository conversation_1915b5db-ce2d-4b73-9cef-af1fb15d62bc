<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\SeoService;

class ServicesController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the services page
     */
    public function index(Request $request): View
    {
        // Get services data from theme config
        $servicesData = theme_config_get('services', []);
        
        // Filter services by category if requested
        $category = $request->get('category', 'all');
        $services = $servicesData['items'] ?? [];
        
        if ($category !== 'all') {
            $services = array_filter($services, function($service) use ($category) {
                return ($service['category'] ?? 'general') === $category;
            });
        }
        
        // Get available categories
        $categories = collect($servicesData['items'] ?? [])
            ->pluck('category')
            ->unique()
            ->filter()
            ->mapWithKeys(function ($cat) {
                return [$cat => ucfirst(str_replace('_', ' ', $cat))];
            })
            ->prepend('All Services', 'all');
        
        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => 'Professional Web Development Services - Zahir Hayrullah',
            'description' => 'Expert web development services including PHP/Laravel development, API integration, database design, and custom web applications. Based in Berlin, Germany.',
            'keywords' => 'web development services, PHP development, Laravel development, API development, database design, custom web applications, Berlin developer',
            'canonical' => url('/services'),
            'og_type' => 'website',
            'og_image' => asset('images/services-og.jpg'),
        ]);
        
        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Services', 'url' => null],
        ];
        
        // Track page visit
        $page = get_from_cache_by_slug('services');
        incrementVisits($request, $page);
        
        return view(theme_view('pages.services'), compact(
            'services',
            'categories',
            'category',
            'seoData',
            'breadcrumbs',
            'page'
        ));
    }
    
    /**
     * Show individual service details
     */
    public function show(string $slug): View
    {
        // Get services data from theme config
        $servicesData = theme_config_get('services', []);
        $services = $servicesData['items'] ?? [];
        
        // Find the specific service
        $service = collect($services)->firstWhere('slug', $slug);
        
        if (!$service) {
            abort(404, 'Service not found');
        }
        
        // Get related services (same category)
        $relatedServices = collect($services)
            ->where('category', $service['category'] ?? 'general')
            ->where('slug', '!=', $slug)
            ->take(3)
            ->values();
        
        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => $service['title'] . ' - Professional Web Development Services',
            'description' => $service['description'] ?? "Learn about my {$service['title']} services. Professional web development solutions tailored to your needs.",
            'keywords' => $service['title'] . ', web development, ' . ($service['category'] ?? 'general') . ', professional services',
            'canonical' => url("/services/{$slug}"),
            'og_type' => 'article',
            'og_image' => $service['image'] ?? asset('images/services-og.jpg'),
        ]);
        
        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Services', 'url' => url('/services')],
            ['name' => $service['title'], 'url' => null],
        ];
        
        return view(theme_view('pages.service-detail'), compact(
            'service',
            'relatedServices',
            'seoData',
            'breadcrumbs'
        ));
    }
    
    /**
     * Get services data for API
     */
    public function api(Request $request)
    {
        $category = $request->get('category');
        $servicesData = theme_config_get('services', []);
        $services = $servicesData['items'] ?? [];
        
        if ($category && $category !== 'all') {
            $services = array_filter($services, function($service) use ($category) {
                return ($service['category'] ?? 'general') === $category;
            });
        }
        
        return response()->json([
            'success' => true,
            'data' => [
                'services' => array_values($services),
                'categories' => collect($services)
                    ->pluck('category')
                    ->unique()
                    ->filter()
                    ->values(),
                'total' => count($services),
            ]
        ]);
    }
}
