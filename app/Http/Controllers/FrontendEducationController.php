<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\SeoService;
use App\Models\Education;
use App\Models\Certification;
use App\Models\Technology;

class FrontendEducationController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the education page
     */
    public function index(Request $request): View
    {
        // Get education from database
        $education = Education::active()
            ->forResume()
            ->ordered()
            ->with(['technologies' => function($query) {
                $query->active()->orderBy('name');
            }])
            ->get();
        
        // Get certifications from database
        $certifications = Certification::active()
            ->ordered()
            ->get();
        
        // Group certifications by provider
        $certificationsByProvider = $certifications->groupBy('provider');
        
        // Get filter parameters
        $institution = $request->get('institution');
        $degree = $request->get('degree');
        $year = $request->get('year');
        
        // Apply filters to education
        if ($institution) {
            $education = $education->where('institution', 'like', "%{$institution}%");
        }
        
        if ($degree) {
            $education = $education->where('degree', 'like', "%{$degree}%");
        }
        
        if ($year) {
            $education = $education->filter(function($edu) use ($year) {
                return $edu->start_date->year <= $year && 
                       ($edu->end_date === null || $edu->end_date->year >= $year);
            });
        }
        
        // Get filter options
        $institutions = Education::active()
            ->select('institution')
            ->distinct()
            ->orderBy('institution')
            ->pluck('institution');
        
        $degrees = Education::active()
            ->select('degree')
            ->distinct()
            ->orderBy('degree')
            ->pluck('degree');
        
        $years = Education::active()
            ->selectRaw('YEAR(start_date) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year');
        
        // Get certification providers
        $providers = Certification::active()
            ->select('provider')
            ->distinct()
            ->orderBy('provider')
            ->pluck('provider');
        
        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => 'Education & Certifications - Zahir Hayrullah\'s Academic Background',
            'description' => 'Explore Zahir Hayrullah\'s educational background including Computer Engineering degree from Sakarya University and professional certifications in web development.',
            'keywords' => 'Zahir Hayrullah education, Computer Engineering, Sakarya University, web development certifications, PHP certifications, professional development',
            'canonical' => url('/education'),
            'og_type' => 'website',
            'og_image' => asset('images/education-og.jpg'),
            'structured_data' => $this->generateEducationStructuredData($education, $certifications),
        ]);
        
        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Education', 'url' => null],
        ];
        
        return view(theme_view('pages.education'), compact(
            'education',
            'certifications',
            'certificationsByProvider',
            'institutions',
            'degrees',
            'years',
            'providers',
            'institution',
            'degree',
            'year',
            'seoData',
            'breadcrumbs'
        ));
    }
    
    /**
     * Get education data for API
     */
    public function api(Request $request)
    {
        $education = Education::active()
            ->forResume()
            ->ordered()
            ->with('technologies')
            ->get();
        
        $certifications = Certification::active()
            ->ordered()
            ->get();
        
        return response()->json([
            'success' => true,
            'data' => [
                'education' => $education,
                'certifications' => $certifications,
                'summary' => [
                    'total_education' => $education->count(),
                    'total_certifications' => $certifications->count(),
                    'latest_degree' => $education->first()?->degree,
                    'latest_institution' => $education->first()?->institution,
                ]
            ]
        ]);
    }
    
    /**
     * Generate structured data for education
     */
    private function generateEducationStructuredData($education, $certifications): array
    {
        $educationalCredentials = [];
        
        // Add formal education
        foreach ($education as $edu) {
            $educationalCredentials[] = [
                '@type' => 'EducationalOccupationalCredential',
                'name' => $edu->degree . ' in ' . $edu->field,
                'description' => $edu->description,
                'educationalLevel' => $edu->degree,
                'recognizedBy' => [
                    '@type' => 'EducationalOrganization',
                    'name' => $edu->institution,
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => $edu->location
                    ]
                ],
                'dateCreated' => $edu->start_date->format('Y-m-d'),
                'expires' => $edu->end_date ? $edu->end_date->format('Y-m-d') : null,
            ];
        }
        
        // Add certifications
        foreach ($certifications as $cert) {
            $educationalCredentials[] = [
                '@type' => 'EducationalOccupationalCredential',
                'name' => $cert->title,
                'description' => $cert->description,
                'credentialCategory' => 'Professional Certification',
                'recognizedBy' => [
                    '@type' => 'Organization',
                    'name' => $cert->provider
                ],
                'dateCreated' => $cert->date,
                'url' => $cert->credential_url,
            ];
        }
        
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Person',
            'name' => 'Zahir Hayrullah',
            'hasCredential' => $educationalCredentials,
            'alumniOf' => $education->map(function($edu) {
                return [
                    '@type' => 'EducationalOrganization',
                    'name' => $edu->institution,
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => $edu->location
                    ]
                ];
            })->toArray(),
        ];
    }
}
