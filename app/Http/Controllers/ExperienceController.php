<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use App\Services\SeoService;
use App\Models\Experience;
use App\Models\Technology;

class ExperienceController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the experience page
     */
    public function index(Request $request): View
    {
        // Get experiences from database
        $experiences = Experience::active()
            ->forResume()
            ->ordered()
            ->with(['technologies' => function ($query) {
                $query->active()->orderBy('name');
            }])
            ->get();

        // Get filter parameters
        $company = $request->get('company');
        $technology = $request->get('technology');
        $year = $request->get('year');

        // Apply filters
        if ($company) {
            $experiences = $experiences->where('company', 'like', "%{$company}%");
        }

        if ($technology) {
            $experiences = $experiences->filter(function ($exp) use ($technology) {
                return $exp->technologies->contains('name', 'like', "%{$technology}%");
            });
        }

        if ($year) {
            $experiences = $experiences->filter(function ($exp) use ($year) {
                return $exp->start_date->year <= $year &&
                    ($exp->end_date === null || $exp->end_date->year >= $year);
            });
        }

        // Get filter options
        $companies = Experience::active()
            ->select('company')
            ->distinct()
            ->orderBy('company')
            ->pluck('company');

        $technologies = Technology::active()
            ->whereHas('experiences')
            ->orderBy('name')
            ->pluck('name');

        $years = Experience::active()
            ->selectRaw('YEAR(start_date) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year');

        // Calculate total experience
        $totalExperience = $this->calculateTotalExperience($experiences);

        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => 'Professional Experience - Zahir Hayrullah\'s Career Journey',
            'description' => 'Explore Zahir Hayrullah\'s professional experience spanning over 7 years in backend development, from junior developer to senior architect across multiple industries.',
            'keywords' => 'Zahir Hayrullah experience, backend developer career, PHP developer experience, Laravel expert, professional journey, Content Fleet, IMTILAK Group',
            'canonical' => url('/experience'),
            'og_type' => 'website',
            'og_image' => asset('images/experience-og.jpg'),
            'structured_data' => $this->generateExperienceStructuredData($experiences),
        ]);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Experience', 'url' => null],
        ];

        return view(theme_view('pages.experience'), compact(
            'experiences',
            'companies',
            'technologies',
            'years',
            'totalExperience',
            'company',
            'technology',
            'year',
            'seoData',
            'breadcrumbs'
        ));
    }

    /**
     * Show individual experience details
     */
    public function show(string $slug): View
    {
        // Find experience by slug (we'll need to add slug to Experience model)
        $experience = Experience::active()
            ->where('slug', $slug)
            ->with(['technologies' => function ($query) {
                $query->active()->orderBy('name');
            }])
            ->firstOrFail();

        // Get related experiences (same company or similar technologies)
        $relatedExperiences = Experience::active()
            ->where('id', '!=', $experience->id)
            ->where(function ($query) use ($experience) {
                $query->where('company', $experience->company)
                    ->orWhereHas('technologies', function ($techQuery) use ($experience) {
                        $techQuery->whereIn(
                            'technology_id',
                            $experience->technologies->pluck('id')->toArray()
                        );
                    });
            })
            ->ordered()
            ->limit(3)
            ->get();

        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => $experience->title . ' at ' . $experience->company . ' - Zahir Hayrullah',
            'description' => "Learn about Zahir Hayrullah's role as {$experience->title} at {$experience->company}. {$experience->description}",
            'keywords' => $experience->title . ', ' . $experience->company . ', backend development, PHP, Laravel, professional experience',
            'canonical' => url("/experience/{$slug}"),
            'og_type' => 'article',
            'og_image' => asset('images/experience-og.jpg'),
        ]);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Experience', 'url' => url('/experience')],
            ['name' => $experience->title . ' at ' . $experience->company, 'url' => null],
        ];

        return view(theme_view('pages.experience-detail'), compact(
            'experience',
            'relatedExperiences',
            'seoData',
            'breadcrumbs'
        ));
    }

    /**
     * Calculate total experience
     */
    private function calculateTotalExperience($experiences): array
    {
        $firstExperience = $experiences->sortBy('start_date')->first();

        if (!$firstExperience) {
            return ['years' => 0, 'months' => 0];
        }

        $totalYears = now()->diffInYears($firstExperience->start_date);
        $totalMonths = now()->diffInMonths($firstExperience->start_date) % 12;

        return [
            'years' => $totalYears,
            'months' => $totalMonths,
            'total_months' => now()->diffInMonths($firstExperience->start_date),
        ];
    }

    /**
     * Get experience data for API
     */
    public function api(Request $request)
    {
        $company = $request->get('company');
        $technology = $request->get('technology');
        $year = $request->get('year');

        $query = Experience::active()
            ->forResume()
            ->ordered()
            ->with('technologies');

        // Apply filters
        if ($company) {
            $query->where('company', 'like', "%{$company}%");
        }

        if ($technology) {
            $query->whereHas('technologies', function ($techQuery) use ($technology) {
                $techQuery->where('name', 'like', "%{$technology}%");
            });
        }

        if ($year) {
            $query->where(function ($dateQuery) use ($year) {
                $dateQuery->whereYear('start_date', '<=', $year)
                    ->where(function ($endQuery) use ($year) {
                        $endQuery->whereNull('end_date')
                            ->orWhereYear('end_date', '>=', $year);
                    });
            });
        }

        $experiences = $query->get();

        return response()->json([
            'success' => true,
            'data' => [
                'experiences' => $experiences,
                'total' => $experiences->count(),
                'companies' => Experience::active()
                    ->select('company')
                    ->distinct()
                    ->orderBy('company')
                    ->pluck('company'),
                'technologies' => Technology::active()
                    ->whereHas('experiences')
                    ->orderBy('name')
                    ->pluck('name'),
                'years' => Experience::active()
                    ->selectRaw('YEAR(start_date) as year')
                    ->distinct()
                    ->orderBy('year', 'desc')
                    ->pluck('year'),
            ]
        ]);
    }

    /**
     * Generate structured data for experiences
     */
    private function generateExperienceStructuredData($experiences): array
    {
        $workHistory = [];

        foreach ($experiences as $experience) {
            $workHistory[] = [
                '@type' => 'WorkExperience',
                'jobTitle' => $experience->title,
                'employer' => [
                    '@type' => 'Organization',
                    'name' => $experience->company,
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => $experience->location
                    ]
                ],
                'startDate' => $experience->start_date->format('Y-m-d'),
                'endDate' => $experience->end_date ? $experience->end_date->format('Y-m-d') : null,
                'description' => $experience->description,
                'skills' => $experience->technologies->pluck('name')->toArray(),
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'Person',
            'name' => 'Zahir Hayrullah',
            'hasOccupation' => $workHistory,
        ];
    }
}
