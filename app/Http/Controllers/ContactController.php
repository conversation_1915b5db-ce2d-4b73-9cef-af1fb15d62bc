<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\SeoService;
use App\Models\Message;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the contact page
     */
    public function index(Request $request): View
    {
        // Get contact data from theme config
        $contactData = theme_config_get('contact', []);

        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => 'Contact ' . personal_name() . ' - Get in Touch for Web Development Projects',
            'description' => 'Contact ' . personal_name() . ' for professional web development services. Based in ' . personal_contact('address') . '. Available for PHP/Laravel projects, API development, and consulting.',
            'keywords' => 'contact ' . personal_name() . ', web development contact, PHP developer ' . personal_contact('city') . ', Laravel developer contact, hire developer',
            'canonical' => url('/contact-me'),
            'og_type' => 'website',
            'og_image' => personal_image('og'),
            'structured_data' => $this->generateContactStructuredData(),
        ]);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Contact Me', 'url' => null],
        ];

        // Track page visit
        $page = get_from_cache_by_slug('contact-me');
        incrementVisits($request, $page);

        return view(theme_view('pages.contact'), compact(
            'contactData',
            'seoData',
            'breadcrumbs',
            'page'
        ));
    }

    /**
     * Store contact form submission
     */
    public function store(Request $request): RedirectResponse
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'budget' => 'nullable|string|max:50',
            'timeline' => 'nullable|string|max:50',
            'g-recaptcha-response' => 'nullable|string', // Add reCAPTCHA if needed
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Store message in database
            $message = Message::create([
                'name' => $request->name,
                'email' => $request->email,
                'subject' => $request->subject,
                'message' => $request->message,
                'phone' => $request->phone,
                'company' => $request->company,
                'budget' => $request->budget,
                'timeline' => $request->timeline,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'status' => 'new',
            ]);

            // Send email notification
            $this->sendContactNotification($message);

            // Send auto-reply to user
            $this->sendAutoReply($message);

            return redirect()->back()->with(
                'success',
                'Thank you for your message! I will get back to you within 24 hours.'
            );
        } catch (\Exception $e) {
            \Log::error('Contact form submission failed: ' . $e->getMessage());

            return redirect()->back()->with(
                'error',
                'Sorry, there was an error sending your message. Please try again or contact me directly.'
            );
        }
    }

    /**
     * Send contact notification email
     */
    private function sendContactNotification(Message $message): void
    {
        try {
            Mail::send('emails.contact-notification', compact('message'), function ($mail) use ($message) {
                $mail->to(personal_contact('email'))
                    ->subject('New Contact Form Submission: ' . $message->subject)
                    ->replyTo($message->email, $message->name);
            });
        } catch (\Exception $e) {
            \Log::error('Failed to send contact notification: ' . $e->getMessage());
        }
    }

    /**
     * Send auto-reply email
     */
    private function sendAutoReply(Message $message): void
    {
        try {
            Mail::send('emails.contact-auto-reply', compact('message'), function ($mail) use ($message) {
                $mail->to($message->email, $message->name)
                    ->subject('Thank you for contacting ' . personal_name())
                    ->from(personal_contact('email'), personal_name());
            });
        } catch (\Exception $e) {
            \Log::error('Failed to send auto-reply: ' . $e->getMessage());
        }
    }

    /**
     * Generate structured data for contact
     */
    private function generateContactStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'ContactPage',
            'name' => 'Contact ' . personal_name(),
            'description' => 'Get in touch with ' . personal_name() . ' for web development projects and consulting.',
            'url' => url('/contact-me'),
            'mainEntity' => generate_personal_schema('person')
        ];
    }
}
