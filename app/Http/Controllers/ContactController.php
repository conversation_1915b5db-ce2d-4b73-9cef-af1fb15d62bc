<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\SeoService;
use App\Models\Message;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the contact page
     */
    public function index(Request $request): View
    {
        // Get contact data from theme config
        $contactData = theme_config_get('contact', []);
        
        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => 'Contact <PERSON><PERSON><PERSON> - Get in Touch for Web Development Projects',
            'description' => 'Contact <PERSON>ahi<PERSON> for professional web development services. Based in Berlin, Germany. Available for PHP/Laravel projects, API development, and consulting.',
            'keywords' => 'contact <PERSON><PERSON><PERSON>, web development contact, PHP developer Berlin, Laravel developer contact, hire developer',
            'canonical' => url('/contact-us'),
            'og_type' => 'website',
            'og_image' => asset('images/contact-og.jpg'),
            'structured_data' => $this->generateContactStructuredData(),
        ]);
        
        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Contact Us', 'url' => null],
        ];
        
        // Track page visit
        $page = get_from_cache_by_slug('contact-us');
        incrementVisits($request, $page);
        
        return view(theme_view('pages.contact'), compact(
            'contactData',
            'seoData',
            'breadcrumbs',
            'page'
        ));
    }
    
    /**
     * Store contact form submission
     */
    public function store(Request $request): RedirectResponse
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'budget' => 'nullable|string|max:50',
            'timeline' => 'nullable|string|max:50',
            'g-recaptcha-response' => 'nullable|string', // Add reCAPTCHA if needed
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Store message in database
            $message = Message::create([
                'name' => $request->name,
                'email' => $request->email,
                'subject' => $request->subject,
                'message' => $request->message,
                'phone' => $request->phone,
                'company' => $request->company,
                'budget' => $request->budget,
                'timeline' => $request->timeline,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'status' => 'new',
            ]);

            // Send email notification
            $this->sendContactNotification($message);

            // Send auto-reply to user
            $this->sendAutoReply($message);

            return redirect()->back()->with('success', 
                'Thank you for your message! I will get back to you within 24 hours.');

        } catch (\Exception $e) {
            \Log::error('Contact form submission failed: ' . $e->getMessage());
            
            return redirect()->back()->with('error', 
                'Sorry, there was an error sending your message. Please try again or contact me directly.');
        }
    }
    
    /**
     * Send contact notification email
     */
    private function sendContactNotification(Message $message): void
    {
        try {
            Mail::send('emails.contact-notification', compact('message'), function ($mail) use ($message) {
                $mail->to('<EMAIL>')
                    ->subject('New Contact Form Submission: ' . $message->subject)
                    ->replyTo($message->email, $message->name);
            });
        } catch (\Exception $e) {
            \Log::error('Failed to send contact notification: ' . $e->getMessage());
        }
    }
    
    /**
     * Send auto-reply email
     */
    private function sendAutoReply(Message $message): void
    {
        try {
            Mail::send('emails.contact-auto-reply', compact('message'), function ($mail) use ($message) {
                $mail->to($message->email, $message->name)
                    ->subject('Thank you for contacting Zahir Hayrullah')
                    ->from('<EMAIL>', 'Zahir Hayrullah');
            });
        } catch (\Exception $e) {
            \Log::error('Failed to send auto-reply: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate structured data for contact
     */
    private function generateContactStructuredData(): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'ContactPage',
            'name' => 'Contact Zahir Hayrullah',
            'description' => 'Get in touch with Zahir Hayrullah for web development projects and consulting.',
            'url' => url('/contact-us'),
            'mainEntity' => [
                '@type' => 'Person',
                'name' => 'Zahir Hayrullah',
                'jobTitle' => 'Senior Backend Developer',
                'email' => '<EMAIL>',
                'telephone' => '+49 ************',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressLocality' => 'Berlin',
                    'addressCountry' => 'Germany'
                ],
                'contactPoint' => [
                    '@type' => 'ContactPoint',
                    'telephone' => '+49 ************',
                    'contactType' => 'customer service',
                    'email' => '<EMAIL>',
                    'availableLanguage' => ['English', 'Arabic', 'Turkish'],
                    'hoursAvailable' => [
                        '@type' => 'OpeningHoursSpecification',
                        'dayOfWeek' => ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
                        'opens' => '09:00',
                        'closes' => '18:00',
                        'timeZone' => 'Europe/Berlin'
                    ]
                ]
            ]
        ];
    }
}
