<?php

namespace App\Http\Controllers;

use App\Models\Technology;
use Illuminate\Http\Request;
use Illuminate\View\View;

class SkillsController extends Controller
{
    /**
     * Display the skills page
     */
    public function index(Request $request): View
    {
        $category = $request->get('category', 'all');
        $proficiency = $request->get('proficiency', 1);
        
        // Get technologies grouped by category
        $technologiesQuery = Technology::active()
            ->forResume()
            ->byProficiency($proficiency)
            ->ordered();
            
        if ($category !== 'all') {
            $technologiesQuery->byCategory($category);
        }
        
        $technologies = $technologiesQuery->get()->groupBy('category');
        
        // Get available categories
        $categories = Technology::active()
            ->select('category')
            ->distinct()
            ->pluck('category')
            ->mapWithKeys(function ($cat) {
                return [$cat => ucfirst($cat)];
            })
            ->prepend('All Categories', 'all');
            
        // Get featured technologies
        $featuredTechnologies = Technology::active()
            ->featured()
            ->ordered()
            ->get();
            
        // SEO data
        $seoData = [
            'title' => 'Technical Skills & Technologies - <PERSON><PERSON><PERSON>',
            'description' => 'Explore my technical expertise in backend development, frontend technologies, databases, and development tools. Proficient in PHP, Laravel, JavaScript, Vue.js, MySQL, and more.',
            'keywords' => 'PHP, Laravel, JavaScript, Vue.js, MySQL, PostgreSQL, Git, technical skills, web development, backend development',
            'canonical' => url('/skills'),
            'og_type' => 'website',
            'og_image' => asset('images/skills-og.jpg'),
        ];
        
        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Skills', 'url' => null],
        ];
        
        return view(theme_view('pages.skills'), compact(
            'technologies',
            'categories',
            'featuredTechnologies',
            'category',
            'proficiency',
            'seoData',
            'breadcrumbs'
        ));
    }
    
    /**
     * Get skills data for API
     */
    public function api(Request $request)
    {
        $category = $request->get('category');
        $proficiency = $request->get('proficiency', 1);
        
        $query = Technology::active()
            ->forResume()
            ->byProficiency($proficiency)
            ->ordered();
            
        if ($category && $category !== 'all') {
            $query->byCategory($category);
        }
        
        $technologies = $query->get()->groupBy('category');
        
        return response()->json([
            'success' => true,
            'data' => [
                'technologies' => $technologies,
                'categories' => Technology::active()
                    ->select('category')
                    ->distinct()
                    ->pluck('category'),
                'featured' => Technology::active()->featured()->get(),
            ]
        ]);
    }
    
    /**
     * Show individual technology details
     */
    public function show(string $slug): View
    {
        $technology = Technology::active()
            ->where('slug', $slug)
            ->firstOrFail();
            
        // Get related technologies (same category)
        $relatedTechnologies = Technology::active()
            ->byCategory($technology->category)
            ->where('id', '!=', $technology->id)
            ->ordered()
            ->limit(6)
            ->get();
            
        // Get experiences using this technology
        $experiences = $technology->experiences()
            ->active()
            ->ordered()
            ->get();
            
        // SEO data
        $seoData = [
            'title' => $technology->name . ' Skills & Experience - Zahir Hayrullah',
            'description' => "Learn about my experience with {$technology->name}. {$technology->description}",
            'keywords' => $technology->name . ', ' . $technology->category . ', web development, programming',
            'canonical' => url("/skills/{$slug}"),
            'og_type' => 'article',
            'og_image' => $technology->icon_url ?? asset('images/skills-og.jpg'),
        ];
        
        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Skills', 'url' => url('/skills')],
            ['name' => $technology->name, 'url' => null],
        ];
        
        return view(theme_view('pages.skill-detail'), compact(
            'technology',
            'relatedTechnologies',
            'experiences',
            'seoData',
            'breadcrumbs'
        ));
    }
}
