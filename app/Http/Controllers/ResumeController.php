<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\Response;
use App\Services\SeoService;
use App\Models\Experience;
use App\Models\Education;
use App\Models\Certification;
use App\Models\Technology;

class ResumeController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Display the unified resume page
     */
    public function index(Request $request): View
    {
        // Get all resume data
        $experiences = Experience::active()
            ->forResume()
            ->ordered()
            ->with(['technologies' => function ($query) {
                $query->active()->orderBy('name');
            }])
            ->get();

        $education = Education::active()
            ->forResume()
            ->ordered()
            ->with(['technologies' => function($query) {
                $query->active()->orderBy('name');
            }])
            ->get();

        $certifications = Certification::active()
            ->ordered()
            ->get();

        // Calculate total experience
        $totalExperience = $this->calculateTotalExperience($experiences);

        // Group certifications by provider
        $certificationsByProvider = $certifications->groupBy('provider');

        // Get filter parameters
        $section = $request->get('section', 'all'); // all, experience, education, certifications
        $company = $request->get('company');
        $technology = $request->get('technology');
        $year = $request->get('year');

        // Apply filters
        if ($company) {
            $experiences = $experiences->where('company', 'like', "%{$company}%");
        }

        if ($technology) {
            $experiences = $experiences->filter(function ($exp) use ($technology) {
                return $exp->technologies->contains('name', 'like', "%{$technology}%");
            });
            
            $education = $education->filter(function ($edu) use ($technology) {
                return $edu->technologies->contains('name', 'like', "%{$technology}%");
            });
        }

        if ($year) {
            $experiences = $experiences->filter(function ($exp) use ($year) {
                return $exp->start_date->year <= $year &&
                    ($exp->end_date === null || $exp->end_date->year >= $year);
            });
            
            $education = $education->filter(function ($edu) use ($year) {
                return $edu->start_date->year <= $year &&
                    ($edu->end_date === null || $edu->end_date->year >= $year);
            });
        }

        // Get filter options
        $companies = Experience::active()
            ->select('company')
            ->distinct()
            ->orderBy('company')
            ->pluck('company');

        $technologies = Technology::active()
            ->where(function($query) {
                $query->whereHas('experiences')
                      ->orWhereHas('education');
            })
            ->orderBy('name')
            ->pluck('name');

        $years = collect()
            ->merge(Experience::active()->selectRaw('YEAR(start_date) as year')->distinct()->pluck('year'))
            ->merge(Education::active()->selectRaw('YEAR(start_date) as year')->distinct()->pluck('year'))
            ->unique()
            ->sort()
            ->reverse()
            ->values();

        // SEO data
        $seoData = $this->seoService->generateSeoData([
            'title' => personal_name() . '\'s Resume - Professional Experience, Education & Certifications',
            'description' => 'Comprehensive resume of ' . personal_name() . ' featuring ' . $totalExperience['years'] . '+ years of professional experience, educational background, and certifications in web development and backend technologies.',
            'keywords' => personal_name() . ' resume, professional experience, education, certifications, backend developer, PHP developer, Laravel expert, web development career',
            'canonical' => url('/resume'),
            'og_type' => 'profile',
            'og_image' => personal_image('profile'),
            'structured_data' => $this->generateResumeStructuredData($experiences, $education, $certifications),
        ]);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Resume', 'url' => null],
        ];

        return view(theme_view('pages.resume'), compact(
            'experiences',
            'education',
            'certifications',
            'certificationsByProvider',
            'totalExperience',
            'companies',
            'technologies',
            'years',
            'section',
            'company',
            'technology',
            'year',
            'seoData',
            'breadcrumbs'
        ));
    }

    /**
     * Download resume PDF
     */
    public function download()
    {
        $resumePath = public_path(personal_resume('path'));

        if (!file_exists($resumePath)) {
            abort(404, 'Resume not found');
        }

        return response()->download($resumePath, personal_resume('download_name'));
    }

    /**
     * Calculate total experience
     */
    private function calculateTotalExperience($experiences): array
    {
        $firstExperience = $experiences->sortBy('start_date')->first();

        if (!$firstExperience) {
            return ['years' => 0, 'months' => 0, 'total_months' => 0];
        }

        $totalYears = now()->diffInYears($firstExperience->start_date);
        $totalMonths = now()->diffInMonths($firstExperience->start_date) % 12;

        return [
            'years' => $totalYears,
            'months' => $totalMonths,
            'total_months' => now()->diffInMonths($firstExperience->start_date),
        ];
    }

    /**
     * Generate structured data for the unified resume
     */
    private function generateResumeStructuredData($experiences, $education, $certifications): array
    {
        $workHistory = [];
        $educationalCredentials = [];

        // Add work experience
        foreach ($experiences as $experience) {
            $workHistory[] = [
                '@type' => 'WorkExperience',
                'jobTitle' => $experience->title,
                'employer' => [
                    '@type' => 'Organization',
                    'name' => $experience->company,
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => $experience->location
                    ]
                ],
                'startDate' => $experience->start_date->format('Y-m-d'),
                'endDate' => $experience->end_date ? $experience->end_date->format('Y-m-d') : null,
                'description' => $experience->description,
                'skills' => $experience->technologies->pluck('name')->toArray(),
            ];
        }

        // Add education
        foreach ($education as $edu) {
            $educationalCredentials[] = [
                '@type' => 'EducationalOccupationalCredential',
                'name' => $edu->degree . ' in ' . $edu->field,
                'description' => $edu->description,
                'educationalLevel' => $edu->degree,
                'recognizedBy' => [
                    '@type' => 'EducationalOrganization',
                    'name' => $edu->institution,
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => $edu->location
                    ]
                ],
                'dateCreated' => $edu->start_date->format('Y-m-d'),
                'expires' => $edu->end_date ? $edu->end_date->format('Y-m-d') : null,
            ];
        }

        // Add certifications
        foreach ($certifications as $cert) {
            $educationalCredentials[] = [
                '@type' => 'EducationalOccupationalCredential',
                'name' => $cert->title,
                'description' => $cert->description,
                'credentialCategory' => 'Professional Certification',
                'recognizedBy' => [
                    '@type' => 'Organization',
                    'name' => $cert->provider
                ],
                'dateCreated' => $cert->date,
                'url' => $cert->credential_url,
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'Person',
            'name' => personal_name(),
            'jobTitle' => personal_professional('title'),
            'description' => personal_bio('short'),
            'email' => personal_contact('email'),
            'telephone' => personal_contact('phone'),
            'address' => [
                '@type' => 'PostalAddress',
                'addressLocality' => personal_contact('city'),
                'addressCountry' => personal_contact('country'),
            ],
            'hasOccupation' => $workHistory,
            'hasCredential' => $educationalCredentials,
            'alumniOf' => $education->map(function($edu) {
                return [
                    '@type' => 'EducationalOrganization',
                    'name' => $edu->institution,
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => $edu->location
                    ]
                ];
            })->toArray(),
            'sameAs' => array_values(array_filter(personal_social())),
        ];
    }
}
