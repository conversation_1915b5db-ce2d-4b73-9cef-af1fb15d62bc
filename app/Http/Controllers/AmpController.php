<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Contracts\Support\Renderable;

class Amp<PERSON>ontroller extends Controller
{

    /**
     * Show the application dashboard.
     *
     * @return Renderable
     */
    public function index()
    {
        $page = get_from_cache_by_slug('home');
        increment_visits($page);
        $profile = User::first()->profile;

        return view('amp.index', compact('page', 'profile'));
    }

    /**
     * Show the application blog index page .
     *
     * @return Renderable
     */
    public function blog()
    {
        $page = get_from_cache_by_slug('blog');
        increment_visits($page);

        return view('amp.blog.index', compact('page'));
    }

    /**
     * Show the application About Me Page .
     *
     * @return Renderable
     */
    public function about_us()
    {
        $page = get_from_cache_by_slug('about-me');
        increment_visits($page);

        return view('amp.pages.about_us', compact('page'));
    }

    /**
     * Show the application Contact Me Page .
     *
     * @return Renderable
     */
    public function contact_us()
    {
        $page = get_from_cache_by_slug('contact-me');
        increment_visits($page);

        return view('amp.pages.contact_us', compact('page'));
    }

    /**
     * Show the application Contact Me Page .
     *
     * @return Renderable
     */
    public function skills()
    {
        $page = get_from_cache_by_slug('skills');
        increment_visits($page);

        return view('amp.pages.skills', compact('page'));
    }

    /**
     * Show the application Contact Me Page .
     *
     * @param $slug
     *
     * @return Renderable
     */

    public function page($slug)
    {
        $page = get_from_cache_by_slug($slug);
        increment_visits($page);

        return view("amp.pages.{$slug}", compact('page'));
    }
}
