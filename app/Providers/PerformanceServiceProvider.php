<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\CacheService;
use App\Services\AssetService;
use App\Observers\ModelCacheObserver;
use App\Models\Technology;
use App\Models\Experience;
use App\Models\Education;
use App\Models\Certification;

class PerformanceServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register CacheService as singleton
        $this->app->singleton(CacheService::class, function ($app) {
            return new CacheService();
        });

        // Register AssetService as singleton
        $this->app->singleton(AssetService::class, function ($app) {
            return new AssetService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register model observers for cache invalidation
        Technology::observe(ModelCacheObserver::class);
        Experience::observe(ModelCacheObserver::class);
        Education::observe(ModelCacheObserver::class);
        Certification::observe(ModelCacheObserver::class);

        // Register view composers for global data
        $this->registerViewComposers();

        // Register console commands
        if ($this->app->runningInConsole()) {
            $this->registerConsoleCommands();
        }
    }

    /**
     * Register view composers
     */
    private function registerViewComposers(): void
    {
        // Global asset service for all views
        view()->composer('*', function ($view) {
            $view->with('assetService', app(AssetService::class));
        });

        // Cache service for specific views
        view()->composer([
            'themes.modern.layout',
            'themes.modern.partials.header',
            'themes.modern.partials.footer'
        ], function ($view) {
            $view->with('cacheService', app(CacheService::class));
        });
    }

    /**
     * Register console commands
     */
    private function registerConsoleCommands(): void
    {
        $this->commands([
            \App\Console\Commands\CacheWarmupCommand::class,
            \App\Console\Commands\CacheClearCommand::class,
        ]);
    }
}
