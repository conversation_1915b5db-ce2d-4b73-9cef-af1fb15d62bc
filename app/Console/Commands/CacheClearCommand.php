<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;
use App\Services\AssetService;

class CacheClearCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-app {--assets : Also clear asset cache}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear application-specific cache';

    /**
     * Execute the console command.
     */
    public function handle(CacheService $cacheService, AssetService $assetService): int
    {
        $this->info('Clearing application cache...');

        try {
            // Clear application cache
            $cacheService->clearAll();
            $this->info('✓ Application cache cleared');

            // Clear asset cache if requested
            if ($this->option('assets')) {
                $assetService->clearAssetCache();
                $this->info('✓ Asset cache cleared');
            }

            $this->info('Cache clearing completed successfully');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Cache clearing failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
