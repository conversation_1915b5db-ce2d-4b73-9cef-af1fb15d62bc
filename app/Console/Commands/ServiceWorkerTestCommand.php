<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class ServiceWorkerTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sw:test {--url=http://localhost}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test service worker functionality';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $baseUrl = $this->option('url');
        
        $this->info('Testing Service Worker functionality...');
        $this->newLine();

        // Test service worker file accessibility
        $this->testServiceWorkerFile($baseUrl);
        
        // Test static assets that should be cached
        $this->testStaticAssets($baseUrl);
        
        $this->newLine();
        $this->info('Service Worker test completed!');
        
        return Command::SUCCESS;
    }

    /**
     * Test if service worker file is accessible
     */
    private function testServiceWorkerFile(string $baseUrl): void
    {
        $this->info('Testing service worker file accessibility...');
        
        try {
            $response = Http::timeout(10)->get($baseUrl . '/sw.js');
            
            if ($response->successful()) {
                $content = $response->body();
                
                // Check if it contains expected content
                if (str_contains($content, 'ZAHIR-CV-v')) {
                    $this->info('✓ Service worker file is accessible and contains expected cache name');
                    
                    // Extract cache name
                    if (preg_match("/const cacheName = '([^']+)'/", $content, $matches)) {
                        $this->line("  Cache name: {$matches[1]}");
                    }
                } else {
                    $this->warn('⚠ Service worker file is accessible but may not be the expected version');
                }
                
                $size = strlen($content);
                $this->line("  File size: " . $this->formatBytes($size));
            } else {
                $this->error("✗ Service worker file not accessible (Status: {$response->status()})");
            }
        } catch (\Exception $e) {
            $this->error('✗ Failed to fetch service worker file: ' . $e->getMessage());
        }
    }

    /**
     * Test static assets that should be cached
     */
    private function testStaticAssets(string $baseUrl): void
    {
        $this->info('Testing static assets...');
        
        $assets = [
            '/' => 'Home page',
            '/about-us' => 'About page',
            '/skills' => 'Skills page',
            '/experience' => 'Experience page',
            '/contact-us' => 'Contact page',
            '/education' => 'Education page'
        ];

        $results = [];
        
        foreach ($assets as $path => $name) {
            try {
                $start = microtime(true);
                $response = Http::timeout(10)->get($baseUrl . $path);
                $end = microtime(true);
                
                $time = round(($end - $start) * 1000, 2);
                
                if ($response->successful()) {
                    $size = strlen($response->body());
                    $results[] = [
                        'name' => $name,
                        'status' => '✓',
                        'time' => $time . 'ms',
                        'size' => $this->formatBytes($size)
                    ];
                } else {
                    $results[] = [
                        'name' => $name,
                        'status' => '✗ ' . $response->status(),
                        'time' => $time . 'ms',
                        'size' => 'N/A'
                    ];
                }
            } catch (\Exception $e) {
                $results[] = [
                    'name' => $name,
                    'status' => '✗ Error',
                    'time' => 'N/A',
                    'size' => 'N/A'
                ];
            }
        }

        // Display results in table
        $this->table(
            ['Asset', 'Status', 'Time', 'Size'],
            collect($results)->map(function ($result) {
                return [
                    $result['name'],
                    $result['status'],
                    $result['time'],
                    $result['size']
                ];
            })->toArray()
        );

        // Summary
        $successful = collect($results)->filter(fn($r) => str_starts_with($r['status'], '✓'))->count();
        $total = count($results);
        
        if ($successful === $total) {
            $this->info("✓ All {$total} static assets are accessible");
        } else {
            $this->warn("⚠ {$successful}/{$total} static assets are accessible");
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
}
