<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;

class CacheWarmupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:warmup {--force : Force cache refresh}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm up application cache with frequently accessed data';

    /**
     * Execute the console command.
     */
    public function handle(CacheService $cacheService): int
    {
        $this->info('Starting cache warmup...');

        if ($this->option('force')) {
            $this->info('Clearing existing cache...');
            $cacheService->clearAll();
        }

        $startTime = microtime(true);

        try {
            // Warm up cache
            $cacheService->warmUp();

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            $this->info("Cache warmup completed successfully in {$duration}ms");

            // Display cache statistics
            $this->displayCacheStats($cacheService);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Cache warmup failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Display cache statistics
     */
    private function displayCacheStats(CacheService $cacheService): void
    {
        $this->info('Cache Statistics:');
        
        $stats = [
            'Technologies (Active)' => $cacheService->getTechnologies()->count(),
            'Technologies (Featured)' => $cacheService->getTechnologies(true, true)->count(),
            'Experiences (Active)' => $cacheService->getExperiences()->count(),
            'Experiences (Featured)' => $cacheService->getExperiences(true, true)->count(),
            'Education' => $cacheService->getEducation()->count(),
            'Certifications (Active)' => $cacheService->getCertifications()->count(),
            'Certifications (Featured)' => $cacheService->getCertifications(true, true)->count(),
        ];

        foreach ($stats as $label => $count) {
            $this->line("  {$label}: {$count} items");
        }
    }
}
