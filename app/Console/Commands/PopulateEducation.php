<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Education;
use App\Models\Certification;
use Illuminate\Support\Str;
use Carbon\Carbon;

class PopulateEducation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:populate-education';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate the education and certifications tables with initial data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting education and certification data population...');

        // Education data
        $educationData = [
            [
                'title' => 'Bachelor of Science',
                'institution' => 'Sakarya University',
                'location' => 'Sakarya, Turkey',
                'degree' => 'Bachelor of Science',
                'field' => 'Computer Engineering',
                'start_date' => '2014-09-01',
                'end_date' => '2018-06-30',
                'description' => 'Comprehensive computer engineering program covering software development, algorithms, data structures, and system design.',
                'coursework' => [
                    'Data Structures and Algorithms',
                    'Object-Oriented Programming',
                    'Database Management Systems',
                    'Software Engineering',
                    'Computer Networks',
                    'Operating Systems'
                ],
                'is_active' => true,
                'show_on_resume' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'High School Diploma',
                'institution' => 'Istiklal High School',
                'location' => 'Aleppo, Syria',
                'degree' => 'High School Diploma',
                'field' => 'Science Track',
                'start_date' => '2011-09-01',
                'end_date' => '2014-06-30',
                'description' => 'Science-focused high school education with emphasis on mathematics, physics, and chemistry.',
                'coursework' => [
                    'Advanced Mathematics',
                    'Physics',
                    'Chemistry',
                    'Computer Science Basics'
                ],
                'is_active' => true,
                'show_on_resume' => false,
                'sort_order' => 2
            ]
        ];

        // Certification data
        $certificationData = [
            [
                'title' => 'Laravel Certified Developer',
                'provider' => 'Laravel',
                'issue_date' => '2022-03-15',
                'description' => 'Official Laravel certification demonstrating expertise in Laravel framework development.',
                'credential_url' => 'https://laravel.com/certification',
                'is_active' => true,
                'show_on_resume' => true,
                'is_featured' => true,
                'never_expires' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'PHP Professional Certification',
                'provider' => 'Zend Technologies',
                'issue_date' => '2021-08-20',
                'description' => 'Professional certification in PHP programming and best practices.',
                'credential_url' => 'https://www.zend.com/training/php-certification',
                'is_active' => true,
                'show_on_resume' => true,
                'is_featured' => true,
                'never_expires' => false,
                'expiry_date' => '2024-08-20',
                'sort_order' => 2
            ],
            [
                'title' => 'AWS Cloud Practitioner',
                'provider' => 'Amazon Web Services',
                'issue_date' => '2023-01-10',
                'description' => 'Foundational certification in AWS cloud services and architecture.',
                'credential_url' => 'https://aws.amazon.com/certification/certified-cloud-practitioner/',
                'is_active' => true,
                'show_on_resume' => true,
                'is_featured' => false,
                'never_expires' => false,
                'expiry_date' => '2026-01-10',
                'sort_order' => 3
            ],
            [
                'title' => 'MySQL Database Administrator',
                'provider' => 'Oracle',
                'issue_date' => '2020-11-05',
                'description' => 'Professional certification in MySQL database administration and optimization.',
                'credential_url' => 'https://education.oracle.com/mysql',
                'is_active' => true,
                'show_on_resume' => true,
                'is_featured' => false,
                'never_expires' => true,
                'sort_order' => 4
            ]
        ];

        $createdEducation = 0;
        $existingEducation = 0;
        $createdCertifications = 0;
        $existingCertifications = 0;

        // Create education records
        foreach ($educationData as $eduData) {
            try {
                $existing = Education::where('title', $eduData['title'])
                    ->where('institution', $eduData['institution'])
                    ->first();

                if (!$existing) {
                    $eduData['start_date'] = Carbon::parse($eduData['start_date']);
                    $eduData['end_date'] = Carbon::parse($eduData['end_date']);

                    Education::create($eduData);
                    $this->line("✓ Created education: {$eduData['title']} at {$eduData['institution']}");
                    $createdEducation++;
                } else {
                    $this->line("- Exists education: {$eduData['title']} at {$eduData['institution']}");
                    $existingEducation++;
                }
            } catch (\Exception $e) {
                $this->error("✗ Error creating education {$eduData['title']}: " . $e->getMessage());
            }
        }

        // Create certification records
        foreach ($certificationData as $certData) {
            try {
                $existing = Certification::where('title', $certData['title'])
                    ->where('provider', $certData['provider'])
                    ->first();

                if (!$existing) {
                    // Parse dates
                    $certData['issue_date'] = Carbon::parse($certData['issue_date']);
                    if (isset($certData['expiry_date'])) {
                        $certData['expiry_date'] = Carbon::parse($certData['expiry_date']);
                    }

                    Certification::create($certData);
                    $this->line("✓ Created certification: {$certData['title']} by {$certData['provider']}");
                    $createdCertifications++;
                } else {
                    $this->line("- Exists certification: {$certData['title']} by {$certData['provider']}");
                    $existingCertifications++;
                }
            } catch (\Exception $e) {
                $this->error("✗ Error creating certification {$certData['title']}: " . $e->getMessage());
            }
        }

        $this->info("Education and certification data population completed!");
        $this->info("Education - Created: {$createdEducation}, Existing: {$existingEducation}");
        $this->info("Certifications - Created: {$createdCertifications}, Existing: {$existingCertifications}");
        $this->info("Total education: " . Education::count());
        $this->info("Total certifications: " . Certification::count());

        return Command::SUCCESS;
    }
}
