<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Technology;
use Illuminate\Support\Str;

class PopulateTechnologies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:populate-technologies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate the technologies table with initial data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting technology data population...');

        $technologies = [
            // Backend Technologies
            ['name' => 'PHP', 'category' => 'backend', 'proficiency_level' => 5, 'years_experience' => 7.0, 'is_featured' => true],
            ['name' => 'Laravel', 'category' => 'backend', 'proficiency_level' => 5, 'years_experience' => 6.0, 'is_featured' => true],
            ['name' => 'Symfony', 'category' => 'backend', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => false],
            ['name' => 'CodeIgniter', 'category' => 'backend', 'proficiency_level' => 4, 'years_experience' => 2.0, 'is_featured' => false],
            ['name' => 'Node.js', 'category' => 'backend', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
            
            // Frontend Technologies
            ['name' => 'JavaScript', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 5.0, 'is_featured' => true],
            ['name' => 'Vue.js', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => true],
            ['name' => 'React', 'category' => 'frontend', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
            ['name' => 'jQuery', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 4.0, 'is_featured' => false],
            ['name' => 'HTML5', 'category' => 'frontend', 'proficiency_level' => 5, 'years_experience' => 7.0, 'is_featured' => true],
            ['name' => 'CSS3', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 6.0, 'is_featured' => true],
            ['name' => 'Bootstrap', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 5.0, 'is_featured' => false],
            ['name' => 'Tailwind CSS', 'category' => 'frontend', 'proficiency_level' => 4, 'years_experience' => 2.0, 'is_featured' => true],
            
            // Database Technologies
            ['name' => 'MySQL', 'category' => 'database', 'proficiency_level' => 5, 'years_experience' => 7.0, 'is_featured' => true],
            ['name' => 'PostgreSQL', 'category' => 'database', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => true],
            ['name' => 'MongoDB', 'category' => 'database', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
            ['name' => 'Redis', 'category' => 'database', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => false],
            ['name' => 'SQLite', 'category' => 'database', 'proficiency_level' => 4, 'years_experience' => 4.0, 'is_featured' => false],
            
            // Tools & DevOps
            ['name' => 'Git', 'category' => 'tools', 'proficiency_level' => 5, 'years_experience' => 7.0, 'is_featured' => true],
            ['name' => 'Docker', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => true],
            ['name' => 'Linux', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 5.0, 'is_featured' => false],
            ['name' => 'Apache', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 5.0, 'is_featured' => false],
            ['name' => 'Nginx', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 4.0, 'is_featured' => false],
            ['name' => 'Composer', 'category' => 'tools', 'proficiency_level' => 5, 'years_experience' => 6.0, 'is_featured' => false],
            ['name' => 'NPM', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 4.0, 'is_featured' => false],
            ['name' => 'Webpack', 'category' => 'tools', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
            ['name' => 'Vite', 'category' => 'tools', 'proficiency_level' => 4, 'years_experience' => 2.0, 'is_featured' => false],
            
            // Cloud & Services
            ['name' => 'AWS', 'category' => 'cloud', 'proficiency_level' => 3, 'years_experience' => 2.0, 'is_featured' => false],
            ['name' => 'DigitalOcean', 'category' => 'cloud', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => false],
            ['name' => 'Cloudflare', 'category' => 'cloud', 'proficiency_level' => 4, 'years_experience' => 3.0, 'is_featured' => false],
        ];

        $created = 0;
        $existing = 0;

        foreach ($technologies as $techData) {
            try {
                $existingTech = Technology::where('name', $techData['name'])->first();
                
                if (!$existingTech) {
                    $techData['slug'] = Str::slug($techData['name']);
                    $techData['is_active'] = true;
                    $techData['sort_order'] = 0;
                    
                    Technology::create($techData);
                    $this->line("✓ Created: {$techData['name']}");
                    $created++;
                } else {
                    $this->line("- Exists: {$techData['name']}");
                    $existing++;
                }
            } catch (\Exception $e) {
                $this->error("✗ Error creating {$techData['name']}: " . $e->getMessage());
            }
        }

        $this->info("Technology data population completed!");
        $this->info("Created: {$created} technologies");
        $this->info("Existing: {$existing} technologies");
        $this->info("Total technologies: " . Technology::count());

        return Command::SUCCESS;
    }
}
