<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class PerformanceTestCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'performance:test {--url=http://localhost} {--requests=10}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test application performance';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $url = $this->option('url');
        $requests = (int) $this->option('requests');
        
        $this->info("Testing performance for: {$url}");
        $this->info("Number of requests: {$requests}");
        $this->newLine();

        $pages = [
            '/' => 'Home',
            '/about-us' => 'About',
            '/skills' => 'Skills',
            '/experience' => 'Experience',
            '/contact-us' => 'Contact'
        ];

        $results = [];

        foreach ($pages as $path => $name) {
            $this->info("Testing {$name} page ({$path})...");
            
            $times = [];
            $sizes = [];
            $errors = 0;

            for ($i = 0; $i < $requests; $i++) {
                $start = microtime(true);
                
                try {
                    $response = Http::timeout(30)->get($url . $path);
                    $end = microtime(true);
                    
                    if ($response->successful()) {
                        $times[] = ($end - $start) * 1000; // Convert to milliseconds
                        $sizes[] = strlen($response->body());
                    } else {
                        $errors++;
                    }
                } catch (\Exception $e) {
                    $errors++;
                }
            }

            if (!empty($times)) {
                $avgTime = round(array_sum($times) / count($times), 2);
                $minTime = round(min($times), 2);
                $maxTime = round(max($times), 2);
                $avgSize = round(array_sum($sizes) / count($sizes));
                
                $results[$name] = [
                    'avg_time' => $avgTime,
                    'min_time' => $minTime,
                    'max_time' => $maxTime,
                    'avg_size' => $avgSize,
                    'errors' => $errors
                ];

                $this->line("  Average: {$avgTime}ms");
                $this->line("  Min: {$minTime}ms");
                $this->line("  Max: {$maxTime}ms");
                $this->line("  Size: " . $this->formatBytes($avgSize));
                
                if ($errors > 0) {
                    $this->error("  Errors: {$errors}");
                }
            } else {
                $this->error("  All requests failed!");
                $results[$name] = ['errors' => $requests];
            }
            
            $this->newLine();
        }

        // Summary
        $this->info('Performance Summary:');
        $this->table(
            ['Page', 'Avg Time (ms)', 'Min Time (ms)', 'Max Time (ms)', 'Size', 'Errors'],
            collect($results)->map(function ($data, $page) {
                return [
                    $page,
                    $data['avg_time'] ?? 'N/A',
                    $data['min_time'] ?? 'N/A',
                    $data['max_time'] ?? 'N/A',
                    isset($data['avg_size']) ? $this->formatBytes($data['avg_size']) : 'N/A',
                    $data['errors'] ?? 0
                ];
            })->toArray()
        );

        // Performance recommendations
        $this->newLine();
        $this->info('Performance Recommendations:');
        
        $totalAvgTime = collect($results)->avg('avg_time');
        if ($totalAvgTime > 1000) {
            $this->warn('• Average response time is over 1 second. Consider optimizing database queries and caching.');
        } elseif ($totalAvgTime > 500) {
            $this->comment('• Response times are acceptable but could be improved with better caching.');
        } else {
            $this->info('• Response times are excellent!');
        }

        $totalAvgSize = collect($results)->avg('avg_size');
        if ($totalAvgSize > 500000) { // 500KB
            $this->warn('• Page sizes are large. Consider image optimization and asset minification.');
        } elseif ($totalAvgSize > 200000) { // 200KB
            $this->comment('• Page sizes are moderate. Consider further optimization.');
        } else {
            $this->info('• Page sizes are well optimized!');
        }

        $totalErrors = collect($results)->sum('errors');
        if ($totalErrors > 0) {
            $this->error("• {$totalErrors} errors occurred during testing. Check application logs.");
        } else {
            $this->info('• No errors occurred during testing!');
        }

        return Command::SUCCESS;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
}
