<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Experience;
use App\Models\Technology;
use Illuminate\Support\Str;
use Carbon\Carbon;

class PopulateExperiences extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:populate-experiences';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate the experiences table with initial data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting experience data population...');

        $experiences = [
            [
                'title' => 'Senior Backend Developer',
                'company' => 'Content Fleet',
                'location' => 'Berlin, Germany',
                'employment_type' => 'full-time',
                'start_date' => '2023-01-01',
                'end_date' => null,
                'is_current' => true,
                'description' => 'Leading backend development initiatives and architecting scalable solutions for content management systems.',
                'responsibilities' => [
                    'Architecting and developing scalable backend systems using PHP and Laravel',
                    'Leading a team of 3 developers in implementing complex features',
                    'Optimizing database performance and implementing caching strategies',
                    'Integrating third-party APIs and developing RESTful services',
                    'Mentoring junior developers and conducting code reviews'
                ],
                'achievements' => [
                    'Improved system performance by 40% through database optimization',
                    'Successfully migrated legacy systems to modern Laravel architecture',
                    'Implemented automated testing reducing bugs by 60%',
                    'Led the development of a microservices architecture'
                ],
                'technologies' => ['PHP', 'Laravel', 'MySQL', 'Redis', 'Docker', 'Git'],
                'is_featured' => true,
                'sort_order' => 1
            ],
            [
                'title' => 'Backend Developer',
                'company' => 'IMTILAK Group',
                'location' => 'Istanbul, Turkey',
                'employment_type' => 'full-time',
                'start_date' => '2021-03-01',
                'end_date' => '2022-12-31',
                'is_current' => false,
                'description' => 'Developed and maintained real estate management systems and customer portals.',
                'responsibilities' => [
                    'Developing backend APIs for real estate management platform',
                    'Implementing user authentication and authorization systems',
                    'Creating automated reporting systems for business analytics',
                    'Maintaining and optimizing existing codebase',
                    'Collaborating with frontend team for seamless integration'
                ],
                'achievements' => [
                    'Developed a comprehensive property management system',
                    'Implemented real-time notifications system',
                    'Reduced API response time by 35%',
                    'Created automated backup and deployment processes'
                ],
                'technologies' => ['PHP', 'Laravel', 'MySQL', 'JavaScript', 'Vue.js', 'Git'],
                'is_featured' => true,
                'sort_order' => 2
            ],
            [
                'title' => 'Full Stack Developer',
                'company' => 'Freelance',
                'location' => 'Remote',
                'employment_type' => 'freelance',
                'start_date' => '2019-06-01',
                'end_date' => '2021-02-28',
                'is_current' => false,
                'description' => 'Provided web development services to various clients, specializing in custom web applications.',
                'responsibilities' => [
                    'Developing custom web applications for small to medium businesses',
                    'Creating responsive frontend interfaces using modern frameworks',
                    'Implementing backend APIs and database design',
                    'Providing technical consultation and project planning',
                    'Managing client relationships and project timelines'
                ],
                'achievements' => [
                    'Successfully delivered 15+ projects on time and within budget',
                    'Built e-commerce platforms generating $100K+ in sales',
                    'Developed custom CMS solutions for content management',
                    'Maintained 98% client satisfaction rate'
                ],
                'technologies' => ['PHP', 'Laravel', 'JavaScript', 'Vue.js', 'MySQL', 'Bootstrap'],
                'is_featured' => false,
                'sort_order' => 3
            ],
            [
                'title' => 'Junior Web Developer',
                'company' => 'Tech Solutions Ltd',
                'location' => 'Sakarya, Turkey',
                'employment_type' => 'full-time',
                'start_date' => '2018-01-01',
                'end_date' => '2019-05-31',
                'is_current' => false,
                'description' => 'Started my professional career developing web applications and learning modern development practices.',
                'responsibilities' => [
                    'Developing frontend interfaces using HTML, CSS, and JavaScript',
                    'Creating backend functionality with PHP and MySQL',
                    'Participating in code reviews and team meetings',
                    'Learning and implementing best practices in web development',
                    'Supporting senior developers in complex projects'
                ],
                'achievements' => [
                    'Quickly mastered Laravel framework within 3 months',
                    'Contributed to 5 major projects during tenure',
                    'Improved code quality through consistent testing practices',
                    'Received recognition for quick learning and adaptation'
                ],
                'technologies' => ['PHP', 'Laravel', 'JavaScript', 'jQuery', 'MySQL', 'HTML5', 'CSS3'],
                'is_featured' => false,
                'sort_order' => 4
            ]
        ];

        $created = 0;
        $existing = 0;

        foreach ($experiences as $expData) {
            try {
                $existingExp = Experience::where('title', $expData['title'])
                    ->where('company', $expData['company'])
                    ->first();
                
                if (!$existingExp) {
                    // Extract technologies for later attachment
                    $technologies = $expData['technologies'];
                    unset($expData['technologies']);
                    
                    // Generate slug
                    $expData['slug'] = Str::slug($expData['title'] . '-' . $expData['company']);
                    $expData['is_active'] = true;
                    $expData['show_on_resume'] = true;
                    
                    // Convert date strings to Carbon instances
                    $expData['start_date'] = Carbon::parse($expData['start_date']);
                    if ($expData['end_date']) {
                        $expData['end_date'] = Carbon::parse($expData['end_date']);
                    }
                    
                    $experience = Experience::create($expData);
                    
                    // Attach technologies
                    foreach ($technologies as $techName) {
                        $technology = Technology::where('name', $techName)->first();
                        if ($technology) {
                            $experience->technologies()->attach($technology->id, [
                                'proficiency_gained' => 'intermediate',
                                'is_primary' => in_array($techName, ['PHP', 'Laravel', 'JavaScript', 'Vue.js']),
                            ]);
                        }
                    }
                    
                    $this->line("✓ Created: {$expData['title']} at {$expData['company']}");
                    $created++;
                } else {
                    $this->line("- Exists: {$expData['title']} at {$expData['company']}");
                    $existing++;
                }
            } catch (\Exception $e) {
                $this->error("✗ Error creating {$expData['title']}: " . $e->getMessage());
            }
        }

        $this->info("Experience data population completed!");
        $this->info("Created: {$created} experiences");
        $this->info("Existing: {$existing} experiences");
        $this->info("Total experiences: " . Experience::count());

        return Command::SUCCESS;
    }
}
