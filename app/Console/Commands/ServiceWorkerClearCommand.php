<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ServiceWorkerClearCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sw:clear {--update-version : Update service worker version}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear service worker cache and optionally update version';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Clearing service worker cache...');

        try {
            if ($this->option('update-version')) {
                $this->updateServiceWorkerVersion();
            }

            $this->info('Service worker cache management completed!');
            $this->info('Users will need to refresh their browsers to get the updated service worker.');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Service worker cache management failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Update service worker version to force cache refresh
     */
    private function updateServiceWorkerVersion(): void
    {
        $swPath = public_path('sw.js');
        
        if (!File::exists($swPath)) {
            $this->warn('Service worker file not found at: ' . $swPath);
            return;
        }

        $content = File::get($swPath);
        
        // Update cache name with timestamp
        $timestamp = now()->format('YmdHis');
        $newCacheName = "ZAHIR-CV-v{$timestamp}";
        
        $updatedContent = preg_replace(
            "/const cacheName = '[^']*'/",
            "const cacheName = '{$newCacheName}'",
            $content
        );

        if ($updatedContent !== $content) {
            File::put($swPath, $updatedContent);
            $this->info("✓ Updated service worker cache name to: {$newCacheName}");
        } else {
            $this->warn('Could not update service worker cache name');
        }
    }
}
