<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AssetOptimizationService;
use Illuminate\Support\Facades\File;

class OptimizeAssetsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assets:optimize {--images : Optimize images only} {--css : Optimize CSS only} {--js : Optimize JavaScript only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize assets for better performance';

    /**
     * Execute the console command.
     */
    public function handle(AssetOptimizationService $optimizer): int
    {
        $this->info('Starting asset optimization...');

        $startTime = microtime(true);
        $optimizedCount = 0;

        try {
            // Optimize images
            if (!$this->option('css') && !$this->option('js')) {
                $optimizedCount += $this->optimizeImages($optimizer);
            }

            // Optimize CSS
            if (!$this->option('images') && !$this->option('js')) {
                $optimizedCount += $this->optimizeCss($optimizer);
            }

            // Optimize JavaScript
            if (!$this->option('images') && !$this->option('css')) {
                $optimizedCount += $this->optimizeJs($optimizer);
            }

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            $this->info("Asset optimization completed successfully!");
            $this->info("Optimized {$optimizedCount} assets in {$duration}ms");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Asset optimization failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Optimize images
     */
    private function optimizeImages(AssetOptimizationService $optimizer): int
    {
        $this->info('Optimizing images...');

        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg'];
        $optimizedCount = 0;

        $publicPath = public_path();
        $imageDirectories = [
            'images',
            'themes/modern/images',
            'uploads'
        ];

        foreach ($imageDirectories as $directory) {
            $fullPath = $publicPath . '/' . $directory;

            if (!File::isDirectory($fullPath)) {
                continue;
            }

            $files = File::allFiles($fullPath);

            foreach ($files as $file) {
                $extension = strtolower($file->getExtension());

                if (in_array($extension, $imageExtensions)) {
                    $relativePath = str_replace($publicPath . '/', '', $file->getPathname());

                    $result = $optimizer->optimizeImage($relativePath);

                    if ($result['optimized']) {
                        $this->line("✓ Optimized: {$relativePath}");
                        $optimizedCount++;
                    } else {
                        $note = isset($result['note']) ? $result['note'] : 'Already optimized';
                        $this->line("- Skipped: {$relativePath} ({$note})");
                    }
                }
            }
        }

        return $optimizedCount;
    }

    /**
     * Optimize CSS files
     */
    private function optimizeCss(AssetOptimizationService $optimizer): int
    {
        $this->info('Optimizing CSS files...');

        $optimizedCount = 0;
        $cssFiles = [
            'themes/modern/css/app.css',
            'themes/modern/css/animations.css'
        ];

        foreach ($cssFiles as $cssFile) {
            $fullPath = public_path($cssFile);

            if (!File::exists($fullPath)) {
                continue;
            }

            $originalContent = File::get($fullPath);
            $optimizedContent = $optimizer->optimizeCss($originalContent);

            // Create minified version
            $minifiedPath = str_replace('.css', '.min.css', $fullPath);
            File::put($minifiedPath, $optimizedContent);

            $originalSize = strlen($originalContent);
            $optimizedSize = strlen($optimizedContent);
            $savings = round((($originalSize - $optimizedSize) / $originalSize) * 100, 1);

            $this->line("✓ Optimized: {$cssFile} (saved {$savings}%)");
            $optimizedCount++;
        }

        return $optimizedCount;
    }

    /**
     * Optimize JavaScript files
     */
    private function optimizeJs(AssetOptimizationService $optimizer): int
    {
        $this->info('Optimizing JavaScript files...');

        $optimizedCount = 0;
        $jsFiles = [
            'themes/modern/js/app.js',
            'themes/modern/js/animations.js'
        ];

        foreach ($jsFiles as $jsFile) {
            $fullPath = public_path($jsFile);

            if (!File::exists($fullPath)) {
                continue;
            }

            $originalContent = File::get($fullPath);
            $optimizedContent = $optimizer->optimizeJs($originalContent);

            // Create minified version
            $minifiedPath = str_replace('.js', '.min.js', $fullPath);
            File::put($minifiedPath, $optimizedContent);

            $originalSize = strlen($originalContent);
            $optimizedSize = strlen($optimizedContent);
            $savings = round((($originalSize - $optimizedSize) / $originalSize) * 100, 1);

            $this->line("✓ Optimized: {$jsFile} (saved {$savings}%)");
            $optimizedCount++;
        }

        return $optimizedCount;
    }
}
