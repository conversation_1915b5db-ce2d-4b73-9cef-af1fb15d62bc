<?php

namespace App\Observers;

use App\Services\CacheService;
use Illuminate\Database\Eloquent\Model;

class ModelCacheObserver
{
    private CacheService $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle the model "created" event.
     */
    public function created(Model $model): void
    {
        $this->clearModelCache($model);
    }

    /**
     * Handle the model "updated" event.
     */
    public function updated(Model $model): void
    {
        $this->clearModelCache($model);
    }

    /**
     * Handle the model "deleted" event.
     */
    public function deleted(Model $model): void
    {
        $this->clearModelCache($model);
    }

    /**
     * Handle the model "restored" event.
     */
    public function restored(Model $model): void
    {
        $this->clearModelCache($model);
    }

    /**
     * Handle the model "force deleted" event.
     */
    public function forceDeleted(Model $model): void
    {
        $this->clearModelCache($model);
    }

    /**
     * Clear cache for the specific model
     */
    private function clearModelCache(Model $model): void
    {
        $modelClass = class_basename($model);
        $this->cacheService->clearModelCache($modelClass);
        
        // Also clear general cache that might be affected
        if (in_array($modelClass, ['Technology', 'Experience', 'Education', 'Certification'])) {
            // Clear navigation and site settings cache as they might include counts
            $this->cacheService->forget('navigation.menu');
            $this->cacheService->forget('site.settings');
        }
    }
}
