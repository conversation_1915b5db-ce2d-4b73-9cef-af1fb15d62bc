<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Certification extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'title',
        'provider',
        'credential_id',
        'issue_date',
        'expiry_date',
        'description',
        'credential_url',
        'verification_url',
        'certificate_image',
        'skills_gained',
        'sort_order',
        'is_active',
        'show_on_resume',
        'is_featured',
        'never_expires',
    ];

    protected $casts = [
        'issue_date' => 'date',
        'expiry_date' => 'date',
        'skills_gained' => 'array',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'show_on_resume' => 'boolean',
        'is_featured' => 'boolean',
        'never_expires' => 'boolean',
    ];

    protected $dates = [
        'issue_date',
        'expiry_date',
        'deleted_at',
    ];

    // uploads image folder name
    public $folderName = 'uploads/certifications';

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForResume($query)
    {
        return $query->where('show_on_resume', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeValid($query)
    {
        return $query->where(function ($q) {
            $q->where('never_expires', true)
              ->orWhere('expiry_date', '>', Carbon::now())
              ->orWhereNull('expiry_date');
        });
    }

    public function scopeExpired($query)
    {
        return $query->where('never_expires', false)
                    ->where('expiry_date', '<=', Carbon::now());
    }

    public function scopeByProvider($query, $provider)
    {
        return $query->where('provider', $provider);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('issue_date', 'desc');
    }

    /**
     * Accessors
     */
    public function getIsValidAttribute(): bool
    {
        if ($this->never_expires) {
            return true;
        }

        if (!$this->expiry_date) {
            return true;
        }

        return $this->expiry_date->isFuture();
    }

    public function getIsExpiredAttribute(): bool
    {
        return !$this->is_valid;
    }

    public function getFormattedIssueDateAttribute(): string
    {
        return $this->issue_date->format('M Y');
    }

    public function getFormattedExpiryDateAttribute(): ?string
    {
        if ($this->never_expires) {
            return 'Never expires';
        }

        return $this->expiry_date ? $this->expiry_date->format('M Y') : null;
    }

    public function getCertificateImageUrlAttribute(): ?string
    {
        if ($this->certificate_image) {
            return asset($this->certificate_image);
        }

        return null;
    }

    public function getValidityStatusAttribute(): string
    {
        if ($this->never_expires) {
            return 'permanent';
        }

        if (!$this->expiry_date) {
            return 'unknown';
        }

        if ($this->expiry_date->isFuture()) {
            return 'valid';
        }

        return 'expired';
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        static::creating(function ($row) {
            if (auth()->check()) {
                $row->created_by = auth()->id();
            }
        });
        
        static::updating(function ($row) {
            if (auth()->check()) {
                $row->modified_by = auth()->id();
            }
        });
        
        static::deleting(function ($row) {
            if (auth()->check()) {
                $row->deleted_by = auth()->id();
                $row->save();
            }
        });
        
        parent::boot();
    }
}
