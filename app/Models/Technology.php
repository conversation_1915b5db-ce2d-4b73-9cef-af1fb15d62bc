<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Technology extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'category',
        'subcategory',
        'proficiency_level',
        'years_experience',
        'icon_path',
        'color',
        'description',
        'official_website',
        'sort_order',
        'is_active',
        'show_on_resume',
        'is_featured',
    ];

    protected $casts = [
        'proficiency_level' => 'integer',
        'years_experience' => 'decimal:1',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'show_on_resume' => 'boolean',
        'is_featured' => 'boolean',
    ];

    protected $dates = [
        'deleted_at',
    ];

    // uploads image folder name
    public $folderName = 'uploads/technologies';

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function experiences(): BelongsToMany
    {
        return $this->belongsToMany(Experience::class, 'experience_technology')
            ->withPivot(['proficiency_gained', 'is_primary'])
            ->withTimestamps();
    }

    public function education(): BelongsToMany
    {
        return $this->belongsToMany(Education::class, 'education_technology')
            ->withPivot(['context', 'is_primary'])
            ->withTimestamps();
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForResume($query)
    {
        return $query->where('show_on_resume', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    public function scopeByProficiency($query, $minLevel = 1)
    {
        return $query->where('proficiency_level', '>=', $minLevel);
    }

    /**
     * Accessors
     */
    public function getProficiencyLabelAttribute(): string
    {
        $labels = [
            1 => 'Beginner',
            2 => 'Basic',
            3 => 'Intermediate',
            4 => 'Advanced',
            5 => 'Expert'
        ];

        return $labels[$this->proficiency_level] ?? 'Unknown';
    }

    public function getProficiencyPercentageAttribute(): int
    {
        return ($this->proficiency_level / 5) * 100;
    }

    public function getIconUrlAttribute(): ?string
    {
        if ($this->icon_path) {
            return asset($this->icon_path);
        }

        // Try to find icon by slug
        $iconPath = "images/logos/{$this->slug}.svg";
        if (file_exists(public_path($iconPath))) {
            return asset($iconPath);
        }

        return null;
    }

    /**
     * Mutators
     */
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = $value;
        $this->attributes['slug'] = Str::slug($value);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        static::creating(function ($row) {
            if (auth()->check()) {
                $row->created_by = auth()->id();
            }
            
            // Auto-generate slug if not provided
            if (empty($row->slug) && !empty($row->name)) {
                $row->slug = Str::slug($row->name);
            }
        });
        
        static::updating(function ($row) {
            if (auth()->check()) {
                $row->modified_by = auth()->id();
            }
        });
        
        static::deleting(function ($row) {
            if (auth()->check()) {
                $row->deleted_by = auth()->id();
                $row->save();
            }
        });
        
        parent::boot();
    }
}
