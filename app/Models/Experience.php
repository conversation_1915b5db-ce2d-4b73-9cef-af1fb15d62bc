<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Carbon\Carbon;

class Experience extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'title',
        'company',
        'location',
        'employment_type',
        'start_date',
        'end_date',
        'is_current',
        'description',
        'responsibilities',
        'achievements',
        'slug',
        'sort_order',
        'is_active',
        'show_on_resume',
        'is_featured',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_current' => 'boolean',
        'responsibilities' => 'array',
        'achievements' => 'array',
        'is_active' => 'boolean',
        'show_on_resume' => 'boolean',
        'is_featured' => 'boolean',
    ];

    protected $dates = [
        'start_date',
        'end_date',
        'deleted_at',
    ];

    // uploads image folder name
    public $folderName = 'uploads/experiences';

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function technologies(): BelongsToMany
    {
        return $this->belongsToMany(Technology::class, 'experience_technology')
            ->withPivot(['proficiency_gained', 'is_primary'])
            ->withTimestamps();
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForResume($query)
    {
        return $query->where('show_on_resume', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeCurrent($query)
    {
        return $query->where('is_current', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('start_date', 'desc');
    }

    /**
     * Accessors
     */
    public function getFormattedDurationAttribute(): string
    {
        $start = $this->start_date;
        $end = $this->end_date ?? Carbon::now();

        $years = $start->diffInYears($end);
        $months = $start->diffInMonths($end) % 12;

        $duration = '';
        if ($years > 0) {
            $duration .= $years . ' year' . ($years > 1 ? 's' : '');
        }
        if ($months > 0) {
            if ($duration) $duration .= ' ';
            $duration .= $months . ' month' . ($months > 1 ? 's' : '');
        }

        return $duration ?: '1 month';
    }

    public function getFormattedDateRangeAttribute(): string
    {
        $start = $this->start_date->format('M Y');
        $end = $this->is_current ? 'Present' : $this->end_date->format('M Y');

        return "{$start} - {$end}";
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        static::creating(function ($row) {
            if (auth()->check()) {
                $row->created_by = auth()->id();
            }

            // Auto-generate slug if not provided
            if (empty($row->slug) && !empty($row->title) && !empty($row->company)) {
                $row->slug = \Illuminate\Support\Str::slug($row->title . '-' . $row->company);
            }
        });

        static::updating(function ($row) {
            if (auth()->check()) {
                $row->modified_by = auth()->id();
            }
        });

        static::deleting(function ($row) {
            if (auth()->check()) {
                $row->deleted_by = auth()->id();
                $row->save();
            }
        });

        parent::boot();
    }
}
