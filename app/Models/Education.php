<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Carbon\Carbon;

class Education extends Model
{
    use SoftDeletes;

    protected $table = 'education';

    protected $fillable = [
        'title',
        'institution',
        'location',
        'degree',
        'field',
        'start_date',
        'end_date',
        'description',
        'coursework',
        'projects',
        'sort_order',
        'is_active',
        'show_on_resume',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'coursework' => 'array',
        'projects' => 'array',
        'is_active' => 'boolean',
        'show_on_resume' => 'boolean',
    ];

    protected $dates = [
        'start_date',
        'end_date',
        'deleted_at',
    ];

    // uploads image folder name
    public $folderName = 'uploads/educations';

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function technologies(): BelongsToMany
    {
        return $this->belongsToMany(Technology::class, 'education_technology')
            ->withPivot(['context', 'is_primary'])
            ->withTimestamps();
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForResume($query)
    {
        return $query->where('show_on_resume', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('start_date', 'desc');
    }

    /**
     * Accessors
     */
    public function getFormattedDurationAttribute(): string
    {
        $start = $this->start_date;
        $end = $this->end_date ?? Carbon::now();

        $years = $start->diffInYears($end);
        $months = $start->diffInMonths($end) % 12;

        $duration = '';
        if ($years > 0) {
            $duration .= $years . ' year' . ($years > 1 ? 's' : '');
        }
        if ($months > 0) {
            if ($duration) $duration .= ' ';
            $duration .= $months . ' month' . ($months > 1 ? 's' : '');
        }

        return $duration ?: '1 month';
    }

    public function getIsCurrentAttribute(): bool
    {
        return is_null($this->end_date);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        static::creating(function ($row) {
            if (auth()->check()) {
                $row->created_by = auth()->id();
            }
        });
        static::deleting(function ($row) {
            $row->deleted_by = auth()->id();
            $row->save();
            if ($row->isForceDeleting()) {
                // Here Write What you want make on delete
                unlinkOldFile($row->image, 'uploads/projects');

                //delete all project images
                $row->images->each->delete();
            }
        });
        parent::boot();
    }
}
