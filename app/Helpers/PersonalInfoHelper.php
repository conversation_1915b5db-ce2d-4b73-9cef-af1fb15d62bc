<?php

if (!function_exists('personal_info')) {
    /**
     * Get personal information from config
     *
     * @param string|null $key
     * @param mixed $default
     * @return mixed
     */
    function personal_info(?string $key = null, $default = null)
    {
        if ($key === null) {
            return config('personal');
        }

        return config("personal.{$key}", $default);
    }
}

if (!function_exists('personal_name')) {
    /**
     * Get personal name
     *
     * @param string $type full|first|last
     * @return string
     */
    function personal_name(string $type = 'full'): string
    {
        return match ($type) {
            'first' => personal_info('first_name'),
            'last' => personal_info('last_name'),
            'full' => personal_info('full_name'),
            default => personal_info('name'),
        };
    }
}

if (!function_exists('personal_contact')) {
    /**
     * Get contact information
     *
     * @param string $type email|phone|phone_link|whatsapp|address
     * @return string
     */
    function personal_contact(string $type): string
    {
        return match ($type) {
            'email' => personal_info('email'),
            'email_link' => 'mailto:' . personal_info('email'),
            'phone' => personal_info('phone'),
            'phone_formatted' => personal_info('phone_formatted'),
            'phone_link' => personal_info('phone_link'),
            'whatsapp' => personal_info('phone_whatsapp_link'),
            'address' => personal_info('current_location'),
            'city' => personal_info('current_city'),
            'country' => personal_info('current_country'),
            'zipcode' => personal_info('zipcode'),
            default => '',
        };
    }
}

if (!function_exists('personal_social')) {
    /**
     * Get social media links
     *
     * @param string|null $platform
     * @return mixed
     */
    function personal_social(?string $platform = null)
    {
        if ($platform === null) {
            return personal_info('social', []);
        }

        return personal_info("social.{$platform}", '');
    }
}

if (!function_exists('personal_professional')) {
    /**
     * Get professional information
     *
     * @param string $type title|profession|subtitle|company|experience|projects
     * @return string|int
     */
    function personal_professional(string $type)
    {
        return match ($type) {
            'title' => personal_info('job_title'),
            'profession' => personal_info('profession'),
            'subtitle' => personal_info('subtitle'),
            'company' => personal_info('company'),
            'experience' => personal_info('years_experience'),
            'projects' => personal_info('projects_completed'),
            default => '',
        };
    }
}

if (!function_exists('personal_bio')) {
    /**
     * Get bio information
     *
     * @param string $type full|short|footer
     * @return string
     */
    function personal_bio(string $type = 'full'): string
    {
        return match ($type) {
            'short' => personal_info('short_bio'),
            'footer' => personal_info('footer_bio'),
            'full' => personal_info('bio'),
            default => personal_info('bio'),
        };
    }
}

if (!function_exists('personal_image')) {
    /**
     * Get personal images
     *
     * @param string $type avatar|profile|about|hero_bg|og|logo
     * @return string
     */
    function personal_image(string $type): string
    {
        $imagePath = personal_info("images.{$type}", '');
        
        if (empty($imagePath)) {
            return '';
        }

        // Return asset URL
        return asset($imagePath);
    }
}

if (!function_exists('personal_resume')) {
    /**
     * Get resume information
     *
     * @param string $type filename|download_name|path|url
     * @return string
     */
    function personal_resume(string $type = 'url'): string
    {
        return match ($type) {
            'filename' => personal_info('resume.filename'),
            'download_name' => personal_info('resume.download_name'),
            'path' => personal_info('resume.path'),
            'url' => url('/download-resume'),
            default => '',
        };
    }
}

if (!function_exists('personal_working_hours')) {
    /**
     * Get working hours information
     *
     * @param string|null $key
     * @return mixed
     */
    function personal_working_hours(?string $key = null)
    {
        if ($key === null) {
            return personal_info('working_hours', []);
        }

        return personal_info("working_hours.{$key}", '');
    }
}

if (!function_exists('personal_education')) {
    /**
     * Get education information
     *
     * @param string|null $key
     * @return mixed
     */
    function personal_education(?string $key = null)
    {
        if ($key === null) {
            return personal_info('education', []);
        }

        return personal_info("education.{$key}", '');
    }
}

if (!function_exists('personal_languages')) {
    /**
     * Get languages
     *
     * @return array
     */
    function personal_languages(): array
    {
        return personal_info('languages', []);
    }
}

if (!function_exists('personal_website')) {
    /**
     * Get website information
     *
     * @param string $type domain|url|name
     * @return string
     */
    function personal_website(string $type = 'url'): string
    {
        return match ($type) {
            'domain' => personal_info('domain'),
            'name' => personal_info('website'),
            'url' => personal_info('website_url'),
            default => personal_info('website_url'),
        };
    }
}

if (!function_exists('generate_personal_schema')) {
    /**
     * Generate structured data for personal information
     *
     * @param string $type person|contact|organization
     * @return array
     */
    function generate_personal_schema(string $type = 'person'): array
    {
        $baseSchema = [
            '@context' => 'https://schema.org',
            'name' => personal_name(),
            'email' => personal_contact('email'),
            'telephone' => personal_contact('phone'),
            'url' => personal_website('url'),
        ];

        return match ($type) {
            'person' => array_merge($baseSchema, [
                '@type' => 'Person',
                'jobTitle' => personal_professional('title'),
                'description' => personal_bio('short'),
                'image' => personal_image('profile'),
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressLocality' => personal_contact('city'),
                    'addressCountry' => personal_contact('country'),
                ],
                'sameAs' => array_values(array_filter(personal_social())),
                'alumniOf' => [
                    '@type' => 'EducationalOrganization',
                    'name' => personal_education('university'),
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => personal_education('university_location'),
                    ]
                ],
                'worksFor' => [
                    '@type' => 'Organization',
                    'name' => personal_professional('company'),
                    'address' => [
                        '@type' => 'PostalAddress',
                        'addressLocality' => personal_contact('city'),
                        'addressCountry' => personal_contact('country'),
                    ]
                ]
            ]),
            'contact' => array_merge($baseSchema, [
                '@type' => 'ContactPoint',
                'contactType' => 'customer service',
                'availableLanguage' => personal_languages(),
                'hoursAvailable' => [
                    '@type' => 'OpeningHoursSpecification',
                    'dayOfWeek' => personal_working_hours('days'),
                    'opens' => personal_working_hours('opens'),
                    'closes' => personal_working_hours('closes'),
                    'timeZone' => personal_working_hours('timezone'),
                ]
            ]),
            'organization' => array_merge($baseSchema, [
                '@type' => 'Organization',
                'logo' => personal_image('logo'),
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressLocality' => personal_contact('city'),
                    'addressCountry' => personal_contact('country'),
                ],
                'contactPoint' => generate_personal_schema('contact'),
            ]),
            default => $baseSchema,
        };
    }
}
